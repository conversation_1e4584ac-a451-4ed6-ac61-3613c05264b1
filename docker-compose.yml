services:
  # Servicio de desarrollo - monta código local
  meteor-dev:
    build:
      context: .
      dockerfile: Dockerfile.dev
    container_name: meteor-dev
    ports:
      - "3000:3000"
    volumes:
      - ./app:/app # Monta directorio local app/ en /app del contenedor
    environment:
      - MONGO_URL=mongodb://${MONGO_INITDB_ROOT_USERNAME:-meteoruser}:${MONGO_INITDB_ROOT_PASSWORD:-meteorpassword}@mongo:27017/${MONGO_DB_NAME:-meteorDev}?authSource=admin
      - ROOT_URL=http://localhost:3000
      - PORT=3000
      - NODE_ENV=development
      - METEOR_ALLOW_SUPERUSER=true
    depends_on:
      - mongo
    # Esto creará una app limpia de Meteor 3 si no existe y luego la ejecutará
    command: >
      sh -c "
        if [ ! -d /app/.meteor ]; then
          echo 'Creando nueva app Meteor 3...'
          cd /app
          meteor create --react --release 3.2.2 .
          meteor npm install
        fi
        
        echo 'Iniciando app Meteor...'
        meteor run --allow-superuser
      "
    networks:
      - meteor-network

  # Servicio de producción
  meteor-prod:
    build:
      context: .
      dockerfile: Dockerfile.prod
    container_name: meteor-prod
    ports:
      - "3000:3000"
    environment:
      - MONGO_URL=mongodb://${MONGO_INITDB_ROOT_USERNAME:-meteoruser}:${MONGO_INITDB_ROOT_PASSWORD:-meteorpassword}@mongo:27017/${MONGO_DB_NAME:-meteorProd}?authSource=admin
      - ROOT_URL=http://localhost:3000
      - PORT=3000
      - NODE_ENV=production
    depends_on:
      - mongo
    profiles:
      - prod
    networks:
      - meteor-network

  # Base de datos MongoDB - Separada en su propio contenedor
  mongo:
    image: mongo:6
    container_name: meteor-mongo
    restart: always
    ports:
      - "27017:27017"
    environment:
      - MONGO_INITDB_ROOT_USERNAME=${MONGO_INITDB_ROOT_USERNAME:-meteoruser}
      - MONGO_INITDB_ROOT_PASSWORD=${MONGO_INITDB_ROOT_PASSWORD:-meteorpassword}
      - MONGO_INITDB_DATABASE=${MONGO_DB_NAME:-meteorDev}
    volumes:
      - mongo-data:/data/db
      - mongo-config:/data/configdb
      - ./mongo-init:/docker-entrypoint-initdb.d
    command: ["--auth", "--wiredTigerCacheSizeGB", "1"]
    networks:
      - meteor-network
    healthcheck:
      test: echo 'db.runCommand("ping").ok' | mongosh localhost:27017/test --quiet
      interval: 10s
      timeout: 10s
      retries: 5
      start_period: 40s

networks:
  meteor-network:
    driver: bridge

volumes:
  mongo-data:
    name: meteor-mongo-data
  mongo-config:
    name: meteor-mongo-config
