#!/usr/bin/env node

// Utilidades para desarrollo de MulbinComponents Backend
const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

const commands = {
  // Comandos de desarrollo
  dev: {
    description: 'Iniciar servidor de desarrollo',
    command: 'meteor run --allow-superuser'
  },
  
  // Comandos de testing
  test: {
    description: 'Ejecutar tests unitarios',
    command: 'meteor test --driver-package meteortesting:mocha'
  },
  
  'test-app': {
    description: 'Ejecutar tests de aplicación completa',
    command: 'meteor test --full-app --driver-package meteortesting:mocha'
  },
  
  'test-watch': {
    description: 'Ejecutar tests en modo watch',
    command: 'TEST_WATCH=1 meteor test --driver-package meteortesting:mocha'
  },
  
  // Comandos de linting
  lint: {
    description: 'Ejecutar linter',
    command: 'eslint . --ext .js'
  },
  
  'lint-fix': {
    description: 'Ejecutar linter y corregir errores automáticamente',
    command: 'eslint . --ext .js --fix'
  },
  
  // Comandos de base de datos
  'reset-db': {
    description: 'Resetear base de datos (CUIDADO: Elimina todos los datos)',
    command: 'meteor reset'
  },
  
  // Comandos de Docker
  'docker-dev': {
    description: 'Iniciar con Docker en modo desarrollo',
    command: 'docker compose up meteor-dev'
  },
  
  'docker-prod': {
    description: 'Iniciar con Docker en modo producción',
    command: 'docker compose --profile prod up meteor-prod'
  },
  
  'docker-build': {
    description: 'Construir imágenes Docker',
    command: 'docker compose build'
  },
  
  'docker-clean': {
    description: 'Limpiar contenedores e imágenes Docker',
    command: 'docker compose down && docker system prune -f'
  },
  
  // Comandos de utilidades
  'check-deps': {
    description: 'Verificar dependencias',
    command: 'meteor npm audit'
  },
  
  'update-deps': {
    description: 'Actualizar dependencias',
    command: 'meteor npm update'
  },
  
  'bundle': {
    description: 'Crear bundle de producción',
    command: 'meteor build ../build --server-only'
  }
};

// Función para ejecutar comandos
function runCommand(cmd) {
  try {
    console.log(`🚀 Ejecutando: ${cmd}`);
    execSync(cmd, { stdio: 'inherit', cwd: process.cwd() });
  } catch (error) {
    console.error(`❌ Error ejecutando comando: ${error.message}`);
    process.exit(1);
  }
}

// Función para mostrar ayuda
function showHelp() {
  console.log('🛠️  MulbinComponents Backend - Utilidades de Desarrollo\n');
  console.log('Uso: node scripts/dev-utils.js <comando>\n');
  console.log('Comandos disponibles:\n');
  
  Object.entries(commands).forEach(([name, config]) => {
    console.log(`  ${name.padEnd(15)} - ${config.description}`);
  });
  
  console.log('\nEjemplos:');
  console.log('  node scripts/dev-utils.js dev          # Iniciar desarrollo');
  console.log('  node scripts/dev-utils.js test         # Ejecutar tests');
  console.log('  node scripts/dev-utils.js docker-dev   # Iniciar con Docker');
  console.log('  node scripts/dev-utils.js lint-fix     # Corregir errores de linting');
}

// Función para verificar estructura del proyecto
function checkProjectStructure() {
  const requiredDirs = [
    'imports/api/collections',
    'imports/api/methods', 
    'imports/api/publications',
    'imports/api/services',
    'imports/api/utils',
    'imports/startup/server',
    'server',
    'tests'
  ];
  
  const requiredFiles = [
    'package.json',
    'server/main.js',
    'imports/startup/server/index.js'
  ];
  
  console.log('🔍 Verificando estructura del proyecto...\n');
  
  let allGood = true;
  
  // Verificar directorios
  requiredDirs.forEach(dir => {
    const fullPath = path.join(process.cwd(), dir);
    if (fs.existsSync(fullPath)) {
      console.log(`✅ ${dir}`);
    } else {
      console.log(`❌ ${dir} - FALTANTE`);
      allGood = false;
    }
  });
  
  // Verificar archivos
  requiredFiles.forEach(file => {
    const fullPath = path.join(process.cwd(), file);
    if (fs.existsSync(fullPath)) {
      console.log(`✅ ${file}`);
    } else {
      console.log(`❌ ${file} - FALTANTE`);
      allGood = false;
    }
  });
  
  if (allGood) {
    console.log('\n🎉 Estructura del proyecto correcta!');
  } else {
    console.log('\n⚠️  Algunos archivos o directorios están faltantes.');
  }
  
  return allGood;
}

// Función para generar datos de desarrollo
function generateDevData() {
  console.log('📊 Generando datos de desarrollo...');
  
  const script = `
    // Script para generar datos de desarrollo
    Meteor.call('demo.cleanAllData', (error, result) => {
      if (error) {
        console.error('Error limpiando datos:', error);
        return;
      }
      
      console.log('Datos limpiados:', result);
      
      // Generar posts de ejemplo
      Meteor.call('demo.generatePosts', 10, (error, result) => {
        if (error) {
          console.error('Error generando posts:', error);
          return;
        }
        
        console.log('Posts generados:', result);
        
        // Generar notificaciones de ejemplo
        Meteor.call('demo.generateNotifications', 5, (error, result) => {
          if (error) {
            console.error('Error generando notificaciones:', error);
            return;
          }
          
          console.log('Notificaciones generadas:', result);
          console.log('✅ Datos de desarrollo generados correctamente');
        });
      });
    });
  `;
  
  // Escribir script temporal
  const scriptPath = path.join(process.cwd(), 'temp-dev-data.js');
  fs.writeFileSync(scriptPath, script);
  
  try {
    runCommand(`meteor shell < ${scriptPath}`);
  } finally {
    // Limpiar script temporal
    if (fs.existsSync(scriptPath)) {
      fs.unlinkSync(scriptPath);
    }
  }
}

// Función principal
function main() {
  const args = process.argv.slice(2);
  const command = args[0];
  
  if (!command || command === 'help' || command === '--help' || command === '-h') {
    showHelp();
    return;
  }
  
  if (command === 'check') {
    checkProjectStructure();
    return;
  }
  
  if (command === 'generate-data') {
    generateDevData();
    return;
  }
  
  if (commands[command]) {
    runCommand(commands[command].command);
  } else {
    console.error(`❌ Comando desconocido: ${command}`);
    console.log('Usa "node scripts/dev-utils.js help" para ver comandos disponibles.');
    process.exit(1);
  }
}

// Ejecutar si es llamado directamente
if (require.main === module) {
  main();
}

module.exports = {
  commands,
  runCommand,
  showHelp,
  checkProjectStructure,
  generateDevData
};
