{"name": "app", "private": true, "scripts": {"start": "meteor run --allow-superuser", "dev": "node scripts/dev-utils.js dev", "test": "meteor test --once --driver-package meteortesting:mocha", "test-app": "TEST_WATCH=1 meteor test --full-app --driver-package meteortesting:mocha", "test-watch": "node scripts/dev-utils.js test-watch", "lint": "node scripts/dev-utils.js lint", "lint-fix": "node scripts/dev-utils.js lint-fix", "docker-dev": "node scripts/dev-utils.js docker-dev", "docker-prod": "node scripts/dev-utils.js docker-prod", "check-structure": "node scripts/dev-utils.js check", "generate-data": "node scripts/dev-utils.js generate-data", "reset-db": "meteor reset", "visualize": "meteor --production --extra-packages bundle-visualizer", "build": "meteor build ../build --server-only"}, "dependencies": {"@babel/runtime": "^7.23.5", "meteor-node-stubs": "^1.2.12", "react": "^18.2.0", "react-dom": "^18.2.0", "uuid": "^11.1.0", "simpl-schema": "^3.4.6"}, "meteor": {"mainModule": {"client": "client/main.jsx", "server": "server/main.js"}, "testModule": "tests/main.js"}}