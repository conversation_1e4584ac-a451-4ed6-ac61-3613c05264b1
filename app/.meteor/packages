# Meteor packages used by this project, one per line.
# Check this file (and the other files in this directory) into your repository.
#
# 'meteor add' and 'meteor remove' will edit this file for you,
# but you can also edit it by hand.

meteor-base@1.5.2             # Packages every Meteor app needs to have
mobile-experience@1.1.2       # Packages for a great mobile UX
mongo@2.1.1                   # The database Meteor supports right now
reactive-var@1.0.13            # Reactive variable for tracker

standard-minifier-css@1.9.3   # CSS minifier run for production mode
standard-minifier-js@3.0.0    # JS minifier run for production mode
es5-shim@4.8.1                # ECMAScript 5 compatibility for older browsers
ecmascript@0.16.10              # Enable ECMAScript2015+ syntax in app code
typescript@5.6.3              # Enable TypeScript syntax in .ts and .tsx modules
shell-server@0.6.1            # Server-side component of the `meteor shell` command
hot-module-replacement@0.5.4  # Update client in development without reloading the page


static-html@1.4.0             # Define static page content in .html files
react-meteor-data       # React higher-order component for reactively tracking Meteor data
jam:easy-schema
ddp-rate-limiter
