// Esquema de validación para Interesados en Posts
import SimpleSchema from 'simpl-schema';

export const InteresadoSchema = new SimpleSchema({
  _id: {
    type: String,
    optional: true
  },
  
  postId: {
    type: String,
    label: 'ID del post'
  },
  
  userId: {
    type: String,
    label: 'ID del usuario interesado'
  },
  
  date: {
    type: Date,
    defaultValue: new Date(),
    label: 'Fecha de interés'
  },
  
  // Campos de auditoría
  createdAt: {
    type: Date,
    optional: true,
    autoValue: function() {
      if (this.isInsert) {
        return new Date();
      } else if (this.isUpsert) {
        return {$setOnInsert: new Date()};
      } else {
        this.unset();
      }
    }
  }
});
