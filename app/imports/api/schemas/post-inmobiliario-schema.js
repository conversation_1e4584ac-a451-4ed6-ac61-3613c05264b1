// Esquema de validación para Posts Inmobiliarios
import SimpleSchema from 'simpl-schema';

export const PostInmobiliarioSchema = new SimpleSchema({
  _id: {
    type: String,
    optional: true
  },
  
  type: {
    type: String,
    allowedValues: ['venta', 'renta', 'socio', 'intercambio'],
    label: 'Tipo de operación'
  },
  
  title: {
    type: String,
    min: 5,
    max: 200,
    label: 'Título del post'
  },
  
  description: {
    type: String,
    min: 20,
    max: 2000,
    label: 'Descripción'
  },
  
  price: {
    type: Number,
    min: 0,
    label: 'Precio'
  },
  
  location: {
    type: String,
    allowedValues: ['norte', 'sur', 'este', 'oeste', 'centro'],
    label: 'Ubicación'
  },
  
  author: {
    type: Object,
    label: 'Autor del post'
  },
  
  'author.id': {
    type: String,
    label: 'ID del autor'
  },
  
  'author.name': {
    type: String,
    label: 'Nombre del autor'
  },
  
  'author.avatar': {
    type: String,
    optional: true,
    label: 'Avatar del autor'
  },
  
  date: {
    type: Date,
    defaultValue: new Date(),
    label: 'Fecha de creación'
  },
  
  images: {
    type: Array,
    optional: true,
    label: 'Imágenes del post'
  },
  
  'images.$': {
    type: String,
    regEx: SimpleSchema.RegEx.Url
  },
  
  interestedCount: {
    type: Number,
    defaultValue: 0,
    min: 0,
    label: 'Cantidad de interesados'
  },
  
  commentsCount: {
    type: Number,
    defaultValue: 0,
    min: 0,
    label: 'Cantidad de comentarios'
  },
  
  // Campos opcionales para detalles adicionales
  bedrooms: {
    type: Number,
    optional: true,
    min: 0,
    label: 'Número de habitaciones'
  },
  
  bathrooms: {
    type: Number,
    optional: true,
    min: 0,
    label: 'Número de baños'
  },
  
  area: {
    type: Number,
    optional: true,
    min: 0,
    label: 'Área en metros cuadrados'
  },
  
  // Campos de auditoría
  createdAt: {
    type: Date,
    optional: true,
    autoValue: function() {
      if (this.isInsert) {
        return new Date();
      } else if (this.isUpsert) {
        return {$setOnInsert: new Date()};
      } else {
        this.unset();
      }
    }
  },
  
  updatedAt: {
    type: Date,
    optional: true,
    autoValue: function() {
      if (this.isUpdate) {
        return new Date();
      }
    }
  }
});
