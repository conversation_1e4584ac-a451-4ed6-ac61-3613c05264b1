// Esquema de validación para Comentarios
import SimpleSchema from 'simpl-schema';

export const ComentarioSchema = new SimpleSchema({
  _id: {
    type: String,
    optional: true
  },
  
  postId: {
    type: String,
    label: 'ID del post'
  },
  
  author: {
    type: Object,
    label: 'Autor del comentario'
  },
  
  'author.id': {
    type: String,
    label: 'ID del autor'
  },
  
  'author.name': {
    type: String,
    label: 'Nombre del autor'
  },
  
  'author.avatar': {
    type: String,
    optional: true,
    label: 'Avatar del autor'
  },
  
  text: {
    type: String,
    min: 1,
    max: 1000,
    label: 'Texto del comentario'
  },
  
  date: {
    type: Date,
    defaultValue: new Date(),
    label: 'Fecha del comentario'
  },
  
  // Campos de auditoría
  createdAt: {
    type: Date,
    optional: true,
    autoValue: function() {
      if (this.isInsert) {
        return new Date();
      } else if (this.isUpsert) {
        return {$setOnInsert: new Date()};
      } else {
        this.unset();
      }
    }
  },
  
  updatedAt: {
    type: Date,
    optional: true,
    autoValue: function() {
      if (this.isUpdate) {
        return new Date();
      }
    }
  }
});
