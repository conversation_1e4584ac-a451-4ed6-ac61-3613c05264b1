// Esquema de validación para Notificaciones
import SimpleSchema from 'simpl-schema';

export const NotificationSchema = new SimpleSchema({
  _id: {
    type: String,
    optional: true
  },
  
  userId: {
    type: String,
    label: 'ID del usuario'
  },
  
  title: {
    type: String,
    min: 1,
    max: 200,
    label: 'Título de la notificación'
  },
  
  message: {
    type: String,
    min: 1,
    max: 1000,
    label: 'Mensaje de la notificación'
  },
  
  type: {
    type: String,
    allowedValues: ['info', 'success', 'warning', 'error'],
    defaultValue: 'info',
    label: 'Tipo de notificación'
  },
  
  read: {
    type: Boolean,
    defaultValue: false,
    label: 'Leída'
  },
  
  readAt: {
    type: Date,
    optional: true,
    label: 'Fecha de lectura'
  },
  
  iconName: {
    type: String,
    optional: true,
    label: 'Nombre del icono'
  },
  
  source: {
    type: String,
    optional: true,
    label: 'Fuente de la notificación'
  },
  
  // Datos adicionales opcionales
  data: {
    type: Object,
    optional: true,
    blackbox: true,
    label: 'Datos adicionales'
  },
  
  // Campos de auditoría
  createdAt: {
    type: Date,
    defaultValue: new Date(),
    autoValue: function() {
      if (this.isInsert) {
        return new Date();
      } else if (this.isUpsert) {
        return {$setOnInsert: new Date()};
      } else {
        this.unset();
      }
    },
    label: 'Fecha de creación'
  },
  
  updatedAt: {
    type: Date,
    optional: true,
    autoValue: function() {
      if (this.isUpdate) {
        return new Date();
      }
    },
    label: 'Fecha de actualización'
  }
});
