// Publicaciones para Posts Inmobiliarios
import { Meteor } from 'meteor/meteor';
import { check } from 'meteor/check';
import { PostsInmobiliarios } from '../collections/posts-inmobiliarios';
import { InteresadosPost } from '../collections/interesados-post';
import { Logger } from '../../startup/server/logging';

if (Meteor.isServer) {
  // Publicación principal de posts inmobiliarios con filtros y paginación
  Meteor.publish('postsInmobiliarios', function (filters = {}, page = 1, limit = 10) {
    check(page, Number);
    check(limit, Number);
    check(filters, {
      type: Match.Optional(String),
      location: Match.Optional(String),
      maxPrice: Match.Optional(Number),
      minPrice: Match.Optional(Number),
      bedrooms: Match.Optional(Number),
      bathrooms: Match.Optional(Number)
    });

    try {
      // Limitar el número máximo de posts por página
      limit = Math.min(limit, 50);
      const skip = (page - 1) * limit;
      const query = {};

      // Aplicar filtros
      if (filters.type) {
        query.type = filters.type;
      }

      if (filters.location) {
        query.location = filters.location;
      }

      if (filters.maxPrice && typeof filters.maxPrice === 'number') {
        query.price = { ...query.price, $lte: filters.maxPrice };
      }

      if (filters.minPrice && typeof filters.minPrice === 'number') {
        query.price = { ...query.price, $gte: filters.minPrice };
      }

      if (filters.bedrooms) {
        query.bedrooms = filters.bedrooms;
      }

      if (filters.bathrooms) {
        query.bathrooms = filters.bathrooms;
      }

      Logger.debug('Posts query', { query, page, limit, userId: this.userId });

      return PostsInmobiliarios.find(query, {
        skip,
        limit,
        sort: { date: -1 },
        fields: {
          // Incluir todos los campos necesarios
          type: 1,
          title: 1,
          description: 1,
          price: 1,
          location: 1,
          author: 1,
          date: 1,
          images: 1,
          interestedCount: 1,
          commentsCount: 1,
          bedrooms: 1,
          bathrooms: 1,
          area: 1,
          createdAt: 1
        }
      });

    } catch (error) {
      Logger.error('Error en publicación postsInmobiliarios', error);
      this.error(new Meteor.Error('publication-error', 'Error al obtener posts'));
    }
  });

  // Publicación para un post específico
  Meteor.publish('postInmobiliario', function (postId) {
    check(postId, String);

    try {
      if (!postId.trim()) {
        throw new Meteor.Error('invalid-postId', 'El postId no puede estar vacío');
      }

      return PostsInmobiliarios.find({ _id: postId });

    } catch (error) {
      Logger.error('Error en publicación postInmobiliario', error);
      this.error(new Meteor.Error('publication-error', 'Error al obtener post'));
    }
  });

  // Publicación para posts del usuario actual
  Meteor.publish('myPosts', function (limit = 20) {
    check(limit, Number);

    if (!this.userId) {
      return this.ready();
    }

    try {
      limit = Math.min(limit, 100);

      return PostsInmobiliarios.find(
        { 'author.id': this.userId },
        {
          limit,
          sort: { date: -1 }
        }
      );

    } catch (error) {
      Logger.error('Error en publicación myPosts', error);
      this.error(new Meteor.Error('publication-error', 'Error al obtener mis posts'));
    }
  });

  // Publicación para posts en los que el usuario está interesado
  Meteor.publish('interestedPosts', function (limit = 20) {
    check(limit, Number);

    if (!this.userId) {
      return this.ready();
    }

    try {
      limit = Math.min(limit, 100);

      // Obtener IDs de posts en los que está interesado
      const intereses = InteresadosPost.find(
        { userId: this.userId },
        { fields: { postId: 1 } }
      ).fetch();

      const postIds = intereses.map(interes => interes.postId);

      if (postIds.length === 0) {
        return this.ready();
      }

      return PostsInmobiliarios.find(
        { _id: { $in: postIds } },
        {
          limit,
          sort: { date: -1 }
        }
      );

    } catch (error) {
      Logger.error('Error en publicación interestedPosts', error);
      this.error(new Meteor.Error('publication-error', 'Error al obtener posts de interés'));
    }
  });

  // Publicación para usuarios interesados en un post específico
  Meteor.publish('postInterests', function (postId) {
    check(postId, String);

    try {
      if (!postId.trim()) {
        throw new Meteor.Error('invalid-postId', 'El postId no puede estar vacío');
      }

      // Solo mostrar si el usuario es el autor del post o está autenticado
      if (!this.userId) {
        return this.ready();
      }

      return InteresadosPost.find({ postId });

    } catch (error) {
      Logger.error('Error en publicación postInterests', error);
      this.error(new Meteor.Error('publication-error', 'Error al obtener intereses'));
    }
  });
}
