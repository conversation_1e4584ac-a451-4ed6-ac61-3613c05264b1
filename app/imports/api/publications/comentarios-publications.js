// Publicaciones para Comentarios
import { Meteor } from 'meteor/meteor';
import { check } from 'meteor/check';
import { ComentariosPost } from '../collections/comentarios-post';
import { PostsInmobiliarios } from '../collections/posts-inmobiliarios';
import { Logger } from '../../startup/server/logging';

if (Meteor.isServer) {
  // Publicación de comentarios para un post específico
  Meteor.publish('comentariosPost', function (postId, limit = 20) {
    check(postId, String);
    check(limit, Number);

    try {
      // Verificar que no sea un string vacío
      if (!postId.trim()) {
        throw new Meteor.Error('invalid-postId', 'El postId no puede estar vacío');
      }

      // Verificar que el post existe
      const post = PostsInmobiliarios.findOne(postId);
      if (!post) {
        throw new Meteor.Error('post-not-found', 'El post no existe');
      }

      // Limitar el número máximo de comentarios
      limit = Math.min(limit, 100);

      Logger.debug('Comentarios query', { postId, limit, userId: this.userId });

      return ComentariosPost.find(
        { postId },
        {
          limit,
          sort: { date: -1 },
          fields: {
            postId: 1,
            author: 1,
            text: 1,
            date: 1,
            createdAt: 1,
            updatedAt: 1
          }
        }
      );

    } catch (error) {
      Logger.error('Error en publicación comentariosPost', error);
      this.error(new Meteor.Error('publication-error', 'Error al obtener comentarios'));
    }
  });

  // Publicación de comentarios del usuario actual
  Meteor.publish('myComments', function (limit = 50) {
    check(limit, Number);

    if (!this.userId) {
      return this.ready();
    }

    try {
      limit = Math.min(limit, 200);

      return ComentariosPost.find(
        { 'author.id': this.userId },
        {
          limit,
          sort: { date: -1 }
        }
      );

    } catch (error) {
      Logger.error('Error en publicación myComments', error);
      this.error(new Meteor.Error('publication-error', 'Error al obtener mis comentarios'));
    }
  });

  // Publicación de comentarios recientes en posts del usuario
  Meteor.publish('commentsOnMyPosts', function (limit = 50) {
    check(limit, Number);

    if (!this.userId) {
      return this.ready();
    }

    try {
      limit = Math.min(limit, 200);

      // Obtener IDs de posts del usuario
      const myPosts = PostsInmobiliarios.find(
        { 'author.id': this.userId },
        { fields: { _id: 1 } }
      ).fetch();

      const postIds = myPosts.map(post => post._id);

      if (postIds.length === 0) {
        return this.ready();
      }

      return ComentariosPost.find(
        { 
          postId: { $in: postIds },
          'author.id': { $ne: this.userId } // Excluir mis propios comentarios
        },
        {
          limit,
          sort: { date: -1 }
        }
      );

    } catch (error) {
      Logger.error('Error en publicación commentsOnMyPosts', error);
      this.error(new Meteor.Error('publication-error', 'Error al obtener comentarios en mis posts'));
    }
  });

  // Publicación de comentarios con paginación
  Meteor.publish('comentariosPaginated', function (postId, page = 1, limit = 10) {
    check(postId, String);
    check(page, Number);
    check(limit, Number);

    try {
      if (!postId.trim()) {
        throw new Meteor.Error('invalid-postId', 'El postId no puede estar vacío');
      }

      // Verificar que el post existe
      const post = PostsInmobiliarios.findOne(postId);
      if (!post) {
        throw new Meteor.Error('post-not-found', 'El post no existe');
      }

      limit = Math.min(limit, 50);
      const skip = (page - 1) * limit;

      return ComentariosPost.find(
        { postId },
        {
          skip,
          limit,
          sort: { date: -1 }
        }
      );

    } catch (error) {
      Logger.error('Error en publicación comentariosPaginated', error);
      this.error(new Meteor.Error('publication-error', 'Error al obtener comentarios paginados'));
    }
  });
}
