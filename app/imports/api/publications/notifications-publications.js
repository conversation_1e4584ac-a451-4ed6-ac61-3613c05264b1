// Publicaciones para Notificaciones
import { Meteor } from 'meteor/meteor';
import { check } from 'meteor/check';
import { Notifications } from '../collections/notifications';
import { Logger } from '../../startup/server/logging';

if (Meteor.isServer) {
  // Publicación de notificaciones para el usuario actual
  Meteor.publish('userNotifications', function (limit = 20) {
    check(limit, Number);

    if (!this.userId) {
      return this.ready();
    }

    try {
      limit = Math.min(limit, 100);

      Logger.debug('Notifications query', { limit, userId: this.userId });

      return Notifications.find(
        { userId: this.userId },
        {
          limit,
          sort: { createdAt: -1 },
          fields: {
            userId: 1,
            title: 1,
            message: 1,
            type: 1,
            read: 1,
            readAt: 1,
            iconName: 1,
            source: 1,
            data: 1,
            createdAt: 1
          }
        }
      );

    } catch (error) {
      Logger.error('Error en publicación userNotifications', error);
      this.error(new Meteor.Error('publication-error', 'Error al obtener notificaciones'));
    }
  });

  // Publicación de notificaciones no leídas
  Meteor.publish('unreadNotifications', function (limit = 50) {
    check(limit, Number);

    if (!this.userId) {
      return this.ready();
    }

    try {
      limit = Math.min(limit, 100);

      return Notifications.find(
        { 
          userId: this.userId,
          read: false 
        },
        {
          limit,
          sort: { createdAt: -1 }
        }
      );

    } catch (error) {
      Logger.error('Error en publicación unreadNotifications', error);
      this.error(new Meteor.Error('publication-error', 'Error al obtener notificaciones no leídas'));
    }
  });

  // Publicación de notificaciones con paginación
  Meteor.publish('notificationsPaginated', function (page = 1, limit = 10, onlyUnread = false) {
    check(page, Number);
    check(limit, Number);
    check(onlyUnread, Boolean);

    if (!this.userId) {
      return this.ready();
    }

    try {
      limit = Math.min(limit, 50);
      const skip = (page - 1) * limit;

      const query = { userId: this.userId };
      if (onlyUnread) {
        query.read = false;
      }

      return Notifications.find(
        query,
        {
          skip,
          limit,
          sort: { createdAt: -1 }
        }
      );

    } catch (error) {
      Logger.error('Error en publicación notificationsPaginated', error);
      this.error(new Meteor.Error('publication-error', 'Error al obtener notificaciones paginadas'));
    }
  });

  // Publicación de notificaciones por tipo
  Meteor.publish('notificationsByType', function (type, limit = 20) {
    check(type, String);
    check(limit, Number);

    if (!this.userId) {
      return this.ready();
    }

    // Validar tipo
    const validTypes = ['info', 'success', 'warning', 'error'];
    if (!validTypes.includes(type)) {
      throw new Meteor.Error('invalid-type', 'Tipo de notificación inválido');
    }

    try {
      limit = Math.min(limit, 100);

      return Notifications.find(
        { 
          userId: this.userId,
          type: type 
        },
        {
          limit,
          sort: { createdAt: -1 }
        }
      );

    } catch (error) {
      Logger.error('Error en publicación notificationsByType', error);
      this.error(new Meteor.Error('publication-error', 'Error al obtener notificaciones por tipo'));
    }
  });

  // Publicación de conteo de notificaciones no leídas
  Meteor.publish('unreadNotificationsCount', function () {
    if (!this.userId) {
      return this.ready();
    }

    try {
      const self = this;
      let count = 0;
      let initializing = true;

      // Observar cambios en las notificaciones no leídas
      const handle = Notifications.find(
        { 
          userId: this.userId,
          read: false 
        }
      ).observeChanges({
        added: function (id) {
          count++;
          if (!initializing) {
            self.changed('notificationsCount', self.userId, { count: count });
          }
        },
        removed: function (id) {
          count--;
          self.changed('notificationsCount', self.userId, { count: count });
        }
      });

      // Marcar como listo después de la inicialización
      initializing = false;
      self.added('notificationsCount', this.userId, { count: count });
      self.ready();

      // Limpiar el observador cuando se desconecte
      self.onStop(function () {
        handle.stop();
      });

    } catch (error) {
      Logger.error('Error en publicación unreadNotificationsCount', error);
      this.error(new Meteor.Error('publication-error', 'Error al obtener conteo de notificaciones'));
    }
  });
}
