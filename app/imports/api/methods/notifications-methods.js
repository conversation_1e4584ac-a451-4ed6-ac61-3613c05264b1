// Métodos para Notificaciones
import { Meteor } from 'meteor/meteor';
import { check } from 'meteor/check';
import { Notifications } from '../collections/notifications';
import { Logger } from '../../startup/server/logging';

Meteor.methods({
  // Marcar notificación como leída
  async 'notifications.markAsRead'(notificationId) {
    check(notificationId, String);

    if (!this.userId) {
      throw new Meteor.Error('not-authorized', 'Debes iniciar sesión');
    }

    try {
      const notification = await Notifications.findOneAsync({
        _id: notificationId,
        userId: this.userId,
      });

      if (!notification) {
        throw new Meteor.Error('not-found', 'Notificación no encontrada');
      }

      await Notifications.updateAsync(
        { _id: notificationId },
        { 
          $set: { 
            read: true,
            readAt: new Date()
          } 
        }
      );

      Logger.info('Notificación marcada como leída', { notificationId, userId: this.userId });
      return true;

    } catch (error) {
      Logger.error('Error al marcar notificación como leída', error);
      throw new Meteor.Error('mark-read-failed', 'Error al marcar como leída');
    }
  },

  // Marcar todas las notificaciones como leídas
  async 'notifications.markAllAsRead'() {
    if (!this.userId) {
      throw new Meteor.Error('not-authorized', 'Debes iniciar sesión');
    }

    try {
      const result = await Notifications.updateAsync(
        { userId: this.userId, read: false },
        { 
          $set: { 
            read: true,
            readAt: new Date()
          } 
        },
        { multi: true }
      );

      Logger.info('Todas las notificaciones marcadas como leídas', { 
        userId: this.userId, 
        count: result 
      });
      return result;

    } catch (error) {
      Logger.error('Error al marcar todas las notificaciones como leídas', error);
      throw new Meteor.Error('mark-all-read-failed', 'Error al marcar todas como leídas');
    }
  },

  // Eliminar notificación
  async 'notifications.remove'(notificationId) {
    check(notificationId, String);

    if (!this.userId) {
      throw new Meteor.Error('not-authorized', 'Debes iniciar sesión');
    }

    try {
      const notification = await Notifications.findOneAsync({
        _id: notificationId,
        userId: this.userId,
      });

      if (!notification) {
        throw new Meteor.Error('not-found', 'Notificación no encontrada');
      }

      await Notifications.removeAsync({ _id: notificationId });

      Logger.info('Notificación eliminada', { notificationId, userId: this.userId });
      return true;

    } catch (error) {
      Logger.error('Error al eliminar notificación', error);
      throw new Meteor.Error('remove-failed', 'Error al eliminar notificación');
    }
  },

  // Obtener conteo de notificaciones no leídas
  async 'notifications.getUnreadCount'() {
    if (!this.userId) {
      return 0;
    }

    try {
      const count = await Notifications.find({
        userId: this.userId,
        read: false,
      }).countAsync();

      return count;

    } catch (error) {
      Logger.error('Error al obtener conteo de notificaciones no leídas', error);
      return 0;
    }
  },

  // Crear una nueva notificación
  async 'notifications.create'(notificationData) {
    check(notificationData, {
      userId: String,
      title: String,
      message: String,
      type: Match.Optional(String),
      iconName: Match.Optional(String),
      data: Match.Optional(Object)
    });

    // Solo permitir crear notificaciones para el usuario actual o ser admin
    if (!this.userId) {
      throw new Meteor.Error('not-authorized', 'Debes iniciar sesión');
    }

    // En un sistema real, aquí verificarías permisos de admin
    // Por ahora, solo permitir crear notificaciones para uno mismo
    if (notificationData.userId !== this.userId) {
      throw new Meteor.Error('not-authorized', 'No puedes crear notificaciones para otros usuarios');
    }

    try {
      const notificationId = await Notifications.insertAsync({
        ...notificationData,
        read: false,
        createdAt: new Date()
      });

      Logger.info('Notificación creada', { notificationId, userId: this.userId });
      return notificationId;

    } catch (error) {
      Logger.error('Error al crear notificación', error);
      throw new Meteor.Error('create-failed', 'Error al crear notificación');
    }
  },

  // Limpiar notificaciones antiguas (solo admin o el propio usuario)
  async 'notifications.cleanup'(olderThanDays = 30) {
    check(olderThanDays, Number);

    if (!this.userId) {
      throw new Meteor.Error('not-authorized', 'Debes iniciar sesión');
    }

    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);

      const result = await Notifications.removeAsync({
        userId: this.userId,
        createdAt: { $lt: cutoffDate },
        read: true // Solo eliminar las que ya fueron leídas
      });

      Logger.info('Notificaciones antiguas eliminadas', { 
        userId: this.userId, 
        count: result,
        olderThanDays 
      });
      return result;

    } catch (error) {
      Logger.error('Error al limpiar notificaciones', error);
      throw new Meteor.Error('cleanup-failed', 'Error al limpiar notificaciones');
    }
  }
});
