// Métodos para generar datos de demostración
import { Meteor } from 'meteor/meteor';
import { check, Match } from 'meteor/check';
import { PostsInmobiliarios } from '../collections/posts-inmobiliarios';
import { ComentariosPost } from '../collections/comentarios-post';
import { Notifications } from '../collections/notifications';
import { Logger } from '../../startup/server/logging';

Meteor.methods({
  // Generar datos de ejemplo para notificaciones
  async 'demo.generateNotifications'(cantidad = 5) {
    check(cantidad, Number);

    // Verificar autenticación (o permitir en desarrollo)
    if (!this.userId && Meteor.isProduction) {
      throw new Meteor.Error('not-authorized', 'Debes iniciar sesión para generar datos de ejemplo');
    }

    const userId = this.userId || 'usuario-demo';

    try {
      // Datos de ejemplo para notificaciones
      const tiposNotificacion = ['info', 'success', 'warning', 'error'];
      const titulos = [
        'Nueva propiedad disponible',
        'Comentario en tu publicación',
        'Actualización del sistema',
        'Te han enviado un mensaje',
        'Recordatorio de cita',
        'Pago realizado con éxito',
        'Aviso importante',
        'Promoción especial',
      ];

      const mensajes = [
        'Se ha agregado una nueva propiedad que coincide con tus criterios de búsqueda.',
        'Alguien ha comentado en tu publicación de propiedad en venta.',
        'El sistema estará en mantenimiento el próximo domingo.',
        'Has recibido un nuevo mensaje de un interesado en tu propiedad.',
        'Tienes una cita programada para mañana a las 10:00 AM.',
        'Tu pago mensual ha sido procesado correctamente.',
        'Es necesario actualizar tus datos personales para continuar usando el servicio.',
        'Aprovecha el 20% de descuento en nuestros servicios premium por tiempo limitado.',
      ];

      const iconos = [
        'home', 'chatbubble', 'warning', 'mail',
        'calendar', 'cash', 'alert', 'pricetag',
      ];

      // Limpiar notificaciones anteriores del usuario con source demo
      await Notifications.removeAsync({ userId, source: 'demo' });

      // Crear nuevas notificaciones
      const notificaciones = [];

      for (let i = 0; i < cantidad; i++) {
        const tipo = tiposNotificacion[Math.floor(Math.random() * tiposNotificacion.length)];
        const tituloIndex = Math.floor(Math.random() * titulos.length);
        const titulo = titulos[tituloIndex];
        const mensaje = mensajes[tituloIndex];
        const iconName = iconos[tituloIndex];

        // Determinar si estará leída o no (70% no leídas para el ejemplo)
        const read = Math.random() > 0.7;

        // Crear fecha aleatoria en los últimos 7 días
        const createdAt = new Date();
        createdAt.setDate(createdAt.getDate() - Math.floor(Math.random() * 7));

        const notificationId = await Notifications.insertAsync({
          userId,
          title: titulo,
          message: mensaje,
          type: tipo,
          read,
          createdAt,
          iconName,
          source: 'demo',
        });

        notificaciones.push(notificationId);
      }

      Logger.info('Notificaciones de demo generadas', { 
        cantidad, 
        userId, 
        notificaciones: notificaciones.length 
      });

      return {
        success: true,
        message: `Se han creado ${cantidad} notificaciones de ejemplo.`,
        notificaciones,
      };

    } catch (error) {
      Logger.error('Error al generar notificaciones de demo', error);
      throw new Meteor.Error('demo-failed', 'Error al generar notificaciones de ejemplo');
    }
  },

  // Limpiar notificaciones de ejemplo
  async 'demo.cleanNotifications'() {
    // Verificar autenticación (o permitir en desarrollo)
    if (!this.userId && Meteor.isProduction) {
      throw new Meteor.Error('not-authorized', 'Debes iniciar sesión para limpiar datos');
    }

    const userId = this.userId || 'usuario-demo';

    try {
      const count = await Notifications.find({
        userId,
        source: 'demo',
      }).countAsync();

      await Notifications.removeAsync({ userId, source: 'demo' });

      Logger.info('Notificaciones de demo eliminadas', { userId, count });

      return {
        success: true,
        message: `Se han eliminado ${count} notificaciones de ejemplo.`,
      };

    } catch (error) {
      Logger.error('Error al limpiar notificaciones de demo', error);
      throw new Meteor.Error('cleanup-failed', 'Error al limpiar notificaciones de ejemplo');
    }
  },

  // Generar posts de ejemplo
  async 'demo.generatePosts'(cantidad = 10) {
    check(cantidad, Number);

    // Solo permitir en desarrollo o si es administrador
    if (!this.userId && Meteor.isProduction) {
      throw new Meteor.Error('not-authorized', 'Solo administradores pueden generar datos de ejemplo en producción');
    }

    try {
      // Verificar si ya hay datos
      const countActual = await PostsInmobiliarios.find().countAsync();
      if (countActual > 0) {
        throw new Meteor.Error('data-exists', `Ya existen ${countActual} posts. Borra los datos existentes primero.`);
      }

      // Datos de ejemplo
      const tipos = ['venta', 'renta', 'socio', 'intercambio'];
      const ubicaciones = ['norte', 'sur', 'este', 'oeste', 'centro'];

      const titulos = [
        'Casa con amplio jardín en zona residencial',
        'Departamento de lujo con vista panorámica',
        'Local comercial en plaza principal',
        'Terreno para desarrollo habitacional',
        'Oficina ejecutiva en zona financiera',
        'Nave industrial con andenes de carga',
        'Casa de campo con alberca',
        'Penthouse de lujo en torre exclusiva',
        'Edificio para remodelación en zona histórica',
        'Terreno con vista al mar',
      ];

      const descripciones = [
        'Excelente propiedad ubicada en zona de alta plusvalía, ideal para familias.',
        'Oportunidad de inversión única, con alta rentabilidad garantizada.',
        'Inmueble totalmente remodelado con acabados de lujo y tecnología de punta.',
        'Ubicación estratégica con fácil acceso a vías principales y transporte público.',
        'Excelentes acabados, distribución óptima y áreas verdes para su comodidad.',
        'Ideal para inversionistas que buscan diversificar su portafolio inmobiliario.',
      ];

      const usuariosDemo = [
        {
          _id: 'usuario1',
          profile: {
            name: 'Carlos Mendoza',
            avatar: 'https://randomuser.me/api/portraits/men/34.jpg',
          },
        },
        {
          _id: 'usuario2',
          profile: {
            name: 'Ana Martínez',
            avatar: 'https://randomuser.me/api/portraits/women/68.jpg',
          },
        },
        {
          _id: 'usuario3',
          profile: {
            name: 'Roberto Sánchez',
            avatar: 'https://randomuser.me/api/portraits/men/45.jpg',
          },
        },
      ];

      const imagenesEjemplo = [
        'https://images.unsplash.com/photo-1568605114967-8130f3a36994',
        'https://images.unsplash.com/photo-1570129477492-45c003edd2be',
        'https://images.unsplash.com/photo-1564013799919-ab600027ffc6',
        'https://images.unsplash.com/photo-1582268611958-ebfd161ef9cf',
        'https://images.unsplash.com/photo-1613490493576-7fde63acd811',
      ];

      // Insertamos posts
      const postIds = [];
      for (let i = 0; i < cantidad; i++) {
        const tipo = tipos[Math.floor(Math.random() * tipos.length)];
        const ubicacion = ubicaciones[Math.floor(Math.random() * ubicaciones.length)];
        const titulo = titulos[Math.floor(Math.random() * titulos.length)];
        const descripcion = descripciones[Math.floor(Math.random() * descripciones.length)];
        const usuario = usuariosDemo[Math.floor(Math.random() * usuariosDemo.length)];
        const precio = Math.floor(Math.random() * 9000000) + 1000000;
        const numImagenes = Math.floor(Math.random() * 4) + 1;

        // Seleccionamos algunas imágenes aleatorias
        const imagenes = [];
        for (let j = 0; j < numImagenes; j++) {
          const img = imagenesEjemplo[Math.floor(Math.random() * imagenesEjemplo.length)];
          if (!imagenes.includes(img)) {
            imagenes.push(img);
          }
        }

        // Datos opcionales para algunos posts
        const tieneDetalles = Math.random() > 0.3;
        const detallesOpcionales = tieneDetalles
          ? {
              bedrooms: Math.floor(Math.random() * 5) + 1,
              bathrooms: Math.floor(Math.random() * 3) + 1,
              area: Math.floor(Math.random() * 500) + 50,
            }
          : {};

        // Creamos una fecha aleatoria en los últimos 30 días
        const fechaCreacion = new Date();
        fechaCreacion.setDate(fechaCreacion.getDate() - Math.floor(Math.random() * 30));

        const postId = await PostsInmobiliarios.insertAsync({
          type: tipo,
          title: `${titulo} #${i + 1}`,
          description: descripcion,
          price: precio,
          location: ubicacion,
          date: fechaCreacion,
          author: {
            id: usuario._id,
            name: usuario.profile.name,
            avatar: usuario.profile.avatar,
          },
          images: imagenes,
          interestedCount: Math.floor(Math.random() * 20),
          commentsCount: 0,
          ...detallesOpcionales,
        });

        postIds.push(postId);
      }

      Logger.info('Posts de demo generados', { cantidad, posts: postIds.length });

      return {
        success: true,
        message: `Se han creado ${cantidad} posts inmobiliarios de ejemplo.`,
        postIds,
      };

    } catch (error) {
      Logger.error('Error al generar posts de demo', error);
      throw new Meteor.Error('demo-failed', 'Error al generar posts de ejemplo');
    }
  },

  // Limpiar todos los datos de ejemplo
  async 'demo.cleanAllData'() {
    // Solo permitir en desarrollo o si es administrador
    if (!this.userId && Meteor.isProduction) {
      throw new Meteor.Error('not-authorized', 'Solo administradores pueden limpiar datos en producción');
    }

    try {
      const countPosts = await PostsInmobiliarios.find().countAsync();
      const countComentarios = await ComentariosPost.find().countAsync();

      // Eliminar todos los registros
      await PostsInmobiliarios.removeAsync({});
      await ComentariosPost.removeAsync({});

      Logger.info('Datos de demo eliminados', { countPosts, countComentarios });

      return {
        success: true,
        message: `Se han eliminado ${countPosts} posts y ${countComentarios} comentarios.`,
      };

    } catch (error) {
      Logger.error('Error al limpiar datos de demo', error);
      throw new Meteor.Error('cleanup-failed', 'Error al limpiar datos de ejemplo');
    }
  },
});
