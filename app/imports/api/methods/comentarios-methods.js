// Métodos para Comentarios
import { Meteor } from 'meteor/meteor';
import { check, Match } from 'meteor/check';
import { ComentariosPost } from '../collections/comentarios-post';
import { PostsInmobiliarios } from '../collections/posts-inmobiliarios';
import { Logger } from '../../startup/server/logging';

Meteor.methods({
  // Agregar comentario a un post
  async 'comments.create'(postId, text) {
    check(postId, String);
    check(text, String);

    // Verificar autenticación
    if (!this.userId) {
      throw new Meteor.Error('not-authorized', 'Debes iniciar sesión para comentar');
    }

    // Validar longitud del texto
    if (text.trim().length < 1 || text.length > 1000) {
      throw new Meteor.Error('invalid-text', 'El comentario debe tener entre 1 y 1000 caracteres');
    }

    try {
      // Validar que existe el post
      const post = await PostsInmobiliarios.findOneAsync(postId);
      if (!post) {
        throw new Meteor.Error('post-not-found', 'La publicación no existe');
      }

      // Obtener datos del usuario
      const user = Meteor.users.findOne(this.userId);
      if (!user) {
        throw new Meteor.Error('user-not-found', 'Usuario no encontrado');
      }

      // Crear comentario
      const comentarioId = await ComentariosPost.insertAsync({
        postId,
        author: {
          id: this.userId,
          name: user.profile?.name || 'Usuario',
          avatar: user.profile?.avatar || '/default-avatar.png',
        },
        text: text.trim(),
        date: new Date(),
      });

      // Actualizar contador de comentarios
      await PostsInmobiliarios.updateAsync(postId, {
        $inc: { commentsCount: 1 },
      });

      Logger.info('Comentario creado', { comentarioId, postId, userId: this.userId });
      return comentarioId;

    } catch (error) {
      Logger.error('Error al crear comentario', error);
      throw new Meteor.Error('comment-create-failed', 'Error al crear el comentario');
    }
  },

  // Actualizar un comentario (solo el autor)
  async 'comments.update'(comentarioId, newText) {
    check(comentarioId, String);
    check(newText, String);

    if (!this.userId) {
      throw new Meteor.Error('not-authorized', 'Debes iniciar sesión');
    }

    // Validar longitud del texto
    if (newText.trim().length < 1 || newText.length > 1000) {
      throw new Meteor.Error('invalid-text', 'El comentario debe tener entre 1 y 1000 caracteres');
    }

    try {
      const comentario = await ComentariosPost.findOneAsync(comentarioId);
      if (!comentario) {
        throw new Meteor.Error('comment-not-found', 'Comentario no encontrado');
      }

      if (comentario.author.id !== this.userId) {
        throw new Meteor.Error('not-authorized', 'Solo el autor puede editar el comentario');
      }

      await ComentariosPost.updateAsync(comentarioId, {
        $set: {
          text: newText.trim(),
          updatedAt: new Date()
        }
      });

      Logger.info('Comentario actualizado', { comentarioId, userId: this.userId });
      return true;

    } catch (error) {
      Logger.error('Error al actualizar comentario', error);
      throw new Meteor.Error('comment-update-failed', 'Error al actualizar el comentario');
    }
  },

  // Eliminar un comentario (solo el autor)
  async 'comments.remove'(comentarioId) {
    check(comentarioId, String);

    if (!this.userId) {
      throw new Meteor.Error('not-authorized', 'Debes iniciar sesión');
    }

    try {
      const comentario = await ComentariosPost.findOneAsync(comentarioId);
      if (!comentario) {
        throw new Meteor.Error('comment-not-found', 'Comentario no encontrado');
      }

      if (comentario.author.id !== this.userId) {
        throw new Meteor.Error('not-authorized', 'Solo el autor puede eliminar el comentario');
      }

      // Eliminar comentario
      await ComentariosPost.removeAsync(comentarioId);

      // Decrementar contador de comentarios
      await PostsInmobiliarios.updateAsync(comentario.postId, {
        $inc: { commentsCount: -1 },
      });

      Logger.info('Comentario eliminado', { comentarioId, userId: this.userId });
      return true;

    } catch (error) {
      Logger.error('Error al eliminar comentario', error);
      throw new Meteor.Error('comment-remove-failed', 'Error al eliminar el comentario');
    }
  },

  // Obtener comentarios de un post con paginación
  async 'comments.getByPost'(postId, page = 1, limit = 10) {
    check(postId, String);
    check(page, Number);
    check(limit, Number);

    // Limitar el número máximo de comentarios por página
    limit = Math.min(limit, 50);

    try {
      const skip = (page - 1) * limit;
      
      const comentarios = await ComentariosPost.find(
        { postId },
        {
          skip,
          limit,
          sort: { date: -1 }
        }
      ).fetchAsync();

      const total = await ComentariosPost.find({ postId }).countAsync();

      return {
        comentarios,
        total,
        page,
        limit,
        hasMore: (skip + limit) < total
      };

    } catch (error) {
      Logger.error('Error al obtener comentarios', error);
      throw new Meteor.Error('comments-fetch-failed', 'Error al obtener comentarios');
    }
  },

  // Método legacy para compatibilidad con frontend existente
  async 'agregarComentarioPost'(postId, text) {
    return await Meteor.callAsync('comments.create', postId, text);
  }
});
