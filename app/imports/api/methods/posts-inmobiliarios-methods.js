// Métodos para Posts Inmobiliarios
import { Meteor } from 'meteor/meteor';
import { check, Match } from 'meteor/check';
import { PostsInmobiliarios } from '../collections/posts-inmobiliarios';
import { InteresadosPost } from '../collections/interesados-post';
import { Logger } from '../../startup/server/logging';

Meteor.methods({
  // Crear un nuevo post inmobiliario
  async 'posts.create'(postData) {
    check(postData, {
      type: String,
      title: String,
      description: String,
      price: Number,
      location: String,
      images: Match.Optional([String]),
      bedrooms: Match.Optional(Number),
      bathrooms: Match.Optional(Number),
      area: Match.Optional(Number)
    });

    // Verificar autenticación
    if (!this.userId) {
      throw new Meteor.Error('not-authorized', 'Debes iniciar sesión para publicar');
    }

    try {
      // Obtener datos del usuario
      const user = Meteor.users.findOne(this.userId);
      if (!user) {
        throw new Meteor.Error('user-not-found', 'Usuario no encontrado');
      }

      // Crear el post
      const postId = await PostsInmobiliarios.insertAsync({
        ...postData,
        author: {
          id: this.userId,
          name: user.profile?.name || 'Usuario',
          avatar: user.profile?.avatar || '/default-avatar.png',
        },
        date: new Date(),
        interestedCount: 0,
        commentsCount: 0,
      });

      Logger.info('Post inmobiliario creado', { postId, userId: this.userId });
      return postId;

    } catch (error) {
      Logger.error('Error al crear post inmobiliario', error);
      throw new Meteor.Error('create-failed', 'Error al crear el post');
    }
  },

  // Marcar/desmarcar interés en un post
  async 'posts.toggleInterest'(postId) {
    check(postId, String);

    // Verificar autenticación
    if (!this.userId) {
      throw new Meteor.Error('not-authorized', 'Debes iniciar sesión para marcar interés');
    }

    try {
      // Verificar si ya está interesado
      const existingInterest = await InteresadosPost.findOneAsync({
        postId,
        userId: this.userId,
      });

      if (existingInterest) {
        // Eliminar interés
        await InteresadosPost.removeAsync(existingInterest._id);
        // Decrementar contador
        await PostsInmobiliarios.updateAsync(postId, {
          $inc: { interestedCount: -1 },
        });
        
        Logger.info('Interés eliminado', { postId, userId: this.userId });
        return false;
      } else {
        // Agregar interés
        await InteresadosPost.insertAsync({
          postId,
          userId: this.userId,
          date: new Date(),
        });
        // Incrementar contador
        await PostsInmobiliarios.updateAsync(postId, {
          $inc: { interestedCount: 1 },
        });
        
        Logger.info('Interés agregado', { postId, userId: this.userId });
        return true;
      }
    } catch (error) {
      Logger.error('Error al toggle interés', error);
      throw new Meteor.Error('toggle-failed', 'Error al procesar interés');
    }
  },

  // Obtener el total de posts (para paginación)
  async 'posts.getTotalCount'(filters = {}) {
    check(filters, {
      type: Match.Optional(String),
      location: Match.Optional(String),
      maxPrice: Match.Optional(Number)
    });

    try {
      const query = {};

      // Aplicar filtros
      if (filters.type) {
        query.type = filters.type;
      }
      if (filters.location) {
        query.location = filters.location;
      }
      if (filters.maxPrice && typeof filters.maxPrice === 'number') {
        query.price = { $lte: filters.maxPrice };
      }

      const count = await PostsInmobiliarios.find(query).countAsync();
      return count;

    } catch (error) {
      Logger.error('Error al obtener conteo de posts', error);
      throw new Meteor.Error('count-failed', 'Error al obtener conteo');
    }
  },

  // Actualizar un post (solo el autor)
  async 'posts.update'(postId, updateData) {
    check(postId, String);
    check(updateData, {
      title: Match.Optional(String),
      description: Match.Optional(String),
      price: Match.Optional(Number),
      location: Match.Optional(String),
      images: Match.Optional([String]),
      bedrooms: Match.Optional(Number),
      bathrooms: Match.Optional(Number),
      area: Match.Optional(Number)
    });

    if (!this.userId) {
      throw new Meteor.Error('not-authorized', 'Debes iniciar sesión');
    }

    try {
      const post = await PostsInmobiliarios.findOneAsync(postId);
      if (!post) {
        throw new Meteor.Error('post-not-found', 'Post no encontrado');
      }

      if (post.author.id !== this.userId) {
        throw new Meteor.Error('not-authorized', 'Solo el autor puede editar el post');
      }

      await PostsInmobiliarios.updateAsync(postId, {
        $set: {
          ...updateData,
          updatedAt: new Date()
        }
      });

      Logger.info('Post actualizado', { postId, userId: this.userId });
      return true;

    } catch (error) {
      Logger.error('Error al actualizar post', error);
      throw new Meteor.Error('update-failed', 'Error al actualizar el post');
    }
  },

  // Eliminar un post (solo el autor)
  async 'posts.remove'(postId) {
    check(postId, String);

    if (!this.userId) {
      throw new Meteor.Error('not-authorized', 'Debes iniciar sesión');
    }

    try {
      const post = await PostsInmobiliarios.findOneAsync(postId);
      if (!post) {
        throw new Meteor.Error('post-not-found', 'Post no encontrado');
      }

      if (post.author.id !== this.userId) {
        throw new Meteor.Error('not-authorized', 'Solo el autor puede eliminar el post');
      }

      // Eliminar post y datos relacionados
      await PostsInmobiliarios.removeAsync(postId);
      await InteresadosPost.removeAsync({ postId });
      
      // También eliminar comentarios
      const { ComentariosPost } = await import('../collections/comentarios-post');
      await ComentariosPost.removeAsync({ postId });

      Logger.info('Post eliminado', { postId, userId: this.userId });
      return true;

    } catch (error) {
      Logger.error('Error al eliminar post', error);
      throw new Meteor.Error('remove-failed', 'Error al eliminar el post');
    }
  },

  // Métodos legacy para compatibilidad con frontend existente
  async 'getTotalPostsCount'(filters = {}) {
    return await Meteor.callAsync('posts.getTotalCount', filters);
  },

  // Método legacy para crear posts (nombre anterior)
  async 'crearPostInmobiliario'(postData) {
    return await Meteor.callAsync('posts.create', postData);
  },

  // Método legacy para toggle de interés (nombre anterior)
  async 'toggleInteresadoPost'(postId) {
    return await Meteor.callAsync('posts.toggleInterest', postId);
  }
});
