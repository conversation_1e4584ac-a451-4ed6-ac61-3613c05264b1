// Colección de Notificaciones
import { Mongo } from 'meteor/mongo';
import { Meteor } from 'meteor/meteor';

// Crear la colección
export const Notifications = new Mongo.Collection('notifications');

// Configuración de seguridad - Solo para desarrollo
if (Meteor.isDevelopment) {
  Notifications.allow({
    insert: function (userId, doc) {
      return userId && doc.userId === userId;
    },
    update: function (userId, doc) {
      return userId && doc.userId === userId;
    },
    remove: function (userId, doc) {
      return userId && doc.userId === userId;
    },
  });
}

// Funciones auxiliares para notificaciones se pueden agregar más tarde si es necesario
// Por ahora las mantenemos simples para evitar dependencias circulares
