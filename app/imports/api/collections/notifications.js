// Colección de Notificaciones
import { Mongo } from 'meteor/mongo';
import { Meteor } from 'meteor/meteor';

// Crear la colección
export const Notifications = new Mongo.Collection('notifications');

// Configuración de seguridad - Solo para desarrollo
if (Meteor.isDevelopment) {
  Notifications.allow({
    insert: function (userId, doc) {
      return userId && doc.userId === userId;
    },
    update: function (userId, doc) {
      return userId && doc.userId === userId;
    },
    remove: function (userId, doc) {
      return userId && doc.userId === userId;
    },
  });
}

// Helpers de la colección
Notifications.helpers({
  // Verificar si la notificación pertenece al usuario
  belongsToUser(userId) {
    return this.userId === userId;
  },

  // Marcar como leída
  markAsRead() {
    return Notifications.update(this._id, {
      $set: { read: true, readAt: new Date() }
    });
  },

  // Obtener tiempo transcurrido desde la creación
  getTimeAgo() {
    const now = new Date();
    const diff = now - this.createdAt;
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (days > 0) return `${days} día${days > 1 ? 's' : ''} atrás`;
    if (hours > 0) return `${hours} hora${hours > 1 ? 's' : ''} atrás`;
    if (minutes > 0) return `${minutes} minuto${minutes > 1 ? 's' : ''} atrás`;
    return 'Hace un momento';
  }
});
