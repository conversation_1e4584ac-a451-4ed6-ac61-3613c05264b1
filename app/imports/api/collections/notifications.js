// Colección de Notificaciones
import { Mongo } from 'meteor/mongo';
import { Meteor } from 'meteor/meteor';

// Crear la colección
export const Notifications = new Mongo.Collection('notifications');

// Configuración de seguridad - Solo para desarrollo
if (Meteor.isDevelopment) {
  Notifications.allow({
    insert: function (userId, doc) {
      return userId && doc.userId === userId;
    },
    update: function (userId, doc) {
      return userId && doc.userId === userId;
    },
    remove: function (userId, doc) {
      return userId && doc.userId === userId;
    },
  });
}

// Funciones auxiliares para notificaciones (en lugar de helpers)
export const NotificationHelpers = {
  // Verificar si la notificación pertenece al usuario
  belongsToUser(notification, userId) {
    return notification.userId === userId;
  },

  // Marcar como leída
  async markAsRead(notificationId) {
    return Notifications.updateAsync(notificationId, {
      $set: { read: true, readAt: new Date() }
    });
  },

  // Obtener tiempo transcurrido desde la creación
  getTimeAgo(createdAt) {
    const now = new Date();
    const diff = now - createdAt;
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (days > 0) return `${days} día${days > 1 ? 's' : ''} atrás`;
    if (hours > 0) return `${hours} hora${hours > 1 ? 's' : ''} atrás`;
    if (minutes > 0) return `${minutes} minuto${minutes > 1 ? 's' : ''} atrás`;
    return 'Hace un momento';
  }
};
