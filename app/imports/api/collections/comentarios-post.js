// Colección de Comentarios de Posts
import { Mongo } from 'meteor/mongo';
import { Meteor } from 'meteor/meteor';

// Crear la colección
export const ComentariosPost = new Mongo.Collection('comentariosPost');

// Configuración de seguridad - Solo para desarrollo
if (Meteor.isDevelopment) {
  ComentariosPost.allow({
    insert: function (userId, doc) {
      return userId && doc.author.id === userId;
    },
    update: function (userId, doc) {
      return userId && doc.author.id === userId;
    },
    remove: function (userId, doc) {
      return userId && doc.author.id === userId;
    },
  });
}

// Funciones auxiliares para comentarios se pueden agregar más tarde si es necesario
// Por ahora las mantenemos simples para evitar dependencias circulares
