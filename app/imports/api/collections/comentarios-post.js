// Colección de Comentarios de Posts
import { Mongo } from 'meteor/mongo';
import { Meteor } from 'meteor/meteor';

// Crear la colección
export const ComentariosPost = new Mongo.Collection('comentariosPost');

// Configuración de seguridad - Solo para desarrollo
if (Meteor.isDevelopment) {
  ComentariosPost.allow({
    insert: function (userId, doc) {
      return userId && doc.author.id === userId;
    },
    update: function (userId, doc) {
      return userId && doc.author.id === userId;
    },
    remove: function (userId, doc) {
      return userId && doc.author.id === userId;
    },
  });
}

// Helpers de la colección
ComentariosPost.helpers({
  // Obtener el post al que pertenece este comentario
  getPost() {
    import { PostsInmobiliarios } from './posts-inmobiliarios';
    return PostsInmobiliarios.findOne(this.postId);
  },

  // Verificar si el comentario pertenece al usuario
  belongsToUser(userId) {
    return this.author.id === userId;
  }
});
