// Colección de Comentarios de Posts
import { Mongo } from 'meteor/mongo';
import { Meteor } from 'meteor/meteor';

// Crear la colección
export const ComentariosPost = new Mongo.Collection('comentariosPost');

// Configuración de seguridad - Solo para desarrollo
if (Meteor.isDevelopment) {
  ComentariosPost.allow({
    insert: function (userId, doc) {
      return userId && doc.author.id === userId;
    },
    update: function (userId, doc) {
      return userId && doc.author.id === userId;
    },
    remove: function (userId, doc) {
      return userId && doc.author.id === userId;
    },
  });
}

// Funciones auxiliares para comentarios (en lugar de helpers)
export const ComentarioHelpers = {
  // Obtener el post al que pertenece este comentario
  getPost(postId) {
    // Importación lazy para evitar dependencias circulares
    const { PostsInmobiliarios } = require('./posts-inmobiliarios');
    return PostsInmobiliarios.findOne(postId);
  },

  // Verificar si el comentario pertenece al usuario
  belongsToUser(comentario, userId) {
    return comentario.author.id === userId;
  }
};
