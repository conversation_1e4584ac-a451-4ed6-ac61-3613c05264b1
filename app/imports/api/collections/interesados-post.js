// Colección de Usuarios Interesados en Posts
import { Mongo } from 'meteor/mongo';
import { InteresadoSchema } from '../schemas/interesado-schema';

// Crear la colección
export const InteresadosPost = new Mongo.Collection('interesadosPost');

// Aplicar esquema de validación
InteresadosPost.attachSchema(InteresadoSchema);

// Configuración de seguridad - Solo para desarrollo
if (Meteor.isDevelopment) {
  InteresadosPost.allow({
    insert: function (userId, doc) {
      return userId && doc.userId === userId;
    },
    update: function (userId, doc) {
      return userId && doc.userId === userId;
    },
    remove: function (userId, doc) {
      return userId && doc.userId === userId;
    },
  });
}

// Helpers de la colección
InteresadosPost.helpers({
  // Obtener el post al que se refiere el interés
  getPost() {
    import { PostsInmobiliarios } from './posts-inmobiliarios';
    return PostsInmobiliarios.findOne(this.postId);
  },

  // Verificar si el interés pertenece al usuario
  belongsToUser(userId) {
    return this.userId === userId;
  },

  // Obtener información del usuario interesado
  getUserInfo() {
    return Meteor.users.findOne(this.userId, {
      fields: { 'profile.name': 1, 'profile.avatar': 1 }
    });
  }
});
