// Colección de Usuarios Interesados en Posts
import { Mongo } from 'meteor/mongo';
import { Meteor } from 'meteor/meteor';

// Crear la colección
export const InteresadosPost = new Mongo.Collection('interesadosPost');

// Configuración de seguridad - Solo para desarrollo
if (Meteor.isDevelopment) {
  InteresadosPost.allow({
    insert: function (userId, doc) {
      return userId && doc.userId === userId;
    },
    update: function (userId, doc) {
      return userId && doc.userId === userId;
    },
    remove: function (userId, doc) {
      return userId && doc.userId === userId;
    },
  });
}

// Funciones auxiliares para interesados (en lugar de helpers)
export const InteresadoHelpers = {
  // Obtener el post al que se refiere el interés
  getPost(postId) {
    // Importación lazy para evitar dependencias circulares
    const { PostsInmobiliarios } = require('./posts-inmobiliarios');
    return PostsInmobiliarios.findOne(postId);
  },

  // Verificar si el interés pertenece al usuario
  belongsToUser(interes, userId) {
    return interes.userId === userId;
  },

  // Obtener información del usuario interesado
  getUserInfo(userId) {
    return Meteor.users.findOne(userId, {
      fields: { 'profile.name': 1, 'profile.avatar': 1 }
    });
  }
};
