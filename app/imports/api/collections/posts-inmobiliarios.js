// Colección de Posts Inmobiliarios
import { Mongo } from 'meteor/mongo';
import { Meteor } from 'meteor/meteor';

// Crear la colección
export const PostsInmobiliarios = new Mongo.Collection('postsInmobiliarios');

// Configuración de seguridad - Solo para desarrollo
// En producción, usar métodos Meteor en lugar de allow/deny
if (Meteor.isDevelopment) {
  PostsInmobiliarios.allow({
    insert: function (userId, doc) {
      return userId && doc.author.id === userId;
    },
    update: function (userId, doc) {
      return userId && doc.author.id === userId;
    },
    remove: function (userId, doc) {
      return userId && doc.author.id === userId;
    },
  });
}

// Helpers de la colección
PostsInmobiliarios.helpers({
  // Obtener comentarios del post
  getComentarios() {
    import { ComentariosPost } from './comentarios-post';
    return ComentariosPost.find({ postId: this._id });
  },

  // Verificar si un usuario está interesado
  isUserInterested(userId) {
    import { InteresadosPost } from './interesados-post';
    return !!InteresadosPost.findOne({ postId: this._id, userId });
  },

  // Obtener lista de usuarios interesados
  getInterestedUsers() {
    import { InteresadosPost } from './interesados-post';
    return InteresadosPost.find({ postId: this._id });
  }
});
