// Colección de Posts Inmobiliarios
import { Mongo } from 'meteor/mongo';
import { Meteor } from 'meteor/meteor';

// Crear la colección
export const PostsInmobiliarios = new Mongo.Collection('postsInmobiliarios');

// Configuración de seguridad - Solo para desarrollo
// En producción, usar métodos Meteor en lugar de allow/deny
if (Meteor.isDevelopment) {
  PostsInmobiliarios.allow({
    insert: function (userId, doc) {
      return userId && doc.author.id === userId;
    },
    update: function (userId, doc) {
      return userId && doc.author.id === userId;
    },
    remove: function (userId, doc) {
      return userId && doc.author.id === userId;
    },
  });
}

// Funciones auxiliares para posts se pueden agregar más tarde si es necesario
// Por ahora las mantenemos simples para evitar dependencias circulares
