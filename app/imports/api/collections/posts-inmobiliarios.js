// Colección de Posts Inmobiliarios
import { Mongo } from 'meteor/mongo';
import { Meteor } from 'meteor/meteor';

// Crear la colección
export const PostsInmobiliarios = new Mongo.Collection('postsInmobiliarios');

// Configuración de seguridad - Solo para desarrollo
// En producción, usar métodos Meteor en lugar de allow/deny
if (Meteor.isDevelopment) {
  PostsInmobiliarios.allow({
    insert: function (userId, doc) {
      return userId && doc.author.id === userId;
    },
    update: function (userId, doc) {
      return userId && doc.author.id === userId;
    },
    remove: function (userId, doc) {
      return userId && doc.author.id === userId;
    },
  });
}

// Funciones auxiliares para posts (en lugar de helpers)
export const PostHelpers = {
  // Obtener comentarios del post
  getComentarios(postId) {
    // Importación lazy para evitar dependencias circulares
    const { ComentariosPost } = require('./comentarios-post');
    return ComentariosPost.find({ postId });
  },

  // Verificar si un usuario está interesado
  isUserInterested(postId, userId) {
    // Importación lazy para evitar dependencias circulares
    const { InteresadosPost } = require('./interesados-post');
    return !!InteresadosPost.findOne({ postId, userId });
  },

  // Obtener lista de usuarios interesados
  getInterestedUsers(postId) {
    // Importación lazy para evitar dependencias circulares
    const { InteresadosPost } = require('./interesados-post');
    return InteresadosPost.find({ postId });
  }
};
