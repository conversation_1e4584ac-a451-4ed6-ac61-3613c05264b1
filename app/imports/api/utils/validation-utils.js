// Utilidades de validación
import { check, Match } from 'meteor/check';

export const ValidationUtils = {
  // Validar email
  isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  },

  // Validar URL
  isValidUrl(url) {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  },

  // Validar ID de MongoDB
  isValidMongoId(id) {
    return typeof id === 'string' && id.length === 24 && /^[0-9a-fA-F]{24}$/.test(id);
  },

  // Validar texto sin caracteres especiales peligrosos
  isSafeText(text) {
    const dangerousChars = /<script|javascript:|data:|vbscript:/i;
    return !dangerousChars.test(text);
  },

  // Limpiar texto de caracteres peligrosos
  sanitizeText(text) {
    if (typeof text !== 'string') return '';
    
    return text
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
      .replace(/javascript:/gi, '')
      .replace(/data:/gi, '')
      .replace(/vbscript:/gi, '')
      .trim();
  },

  // Validar rango de números
  isInRange(value, min, max) {
    return typeof value === 'number' && value >= min && value <= max;
  },

  // Validar longitud de string
  isValidLength(str, minLength, maxLength) {
    return typeof str === 'string' && 
           str.length >= minLength && 
           str.length <= maxLength;
  },

  // Validar que un valor esté en una lista de valores permitidos
  isInAllowedValues(value, allowedValues) {
    return allowedValues.includes(value);
  },

  // Validar datos de post inmobiliario
  validatePostData(data) {
    const errors = [];

    try {
      check(data, {
        type: String,
        title: String,
        description: String,
        price: Number,
        location: String,
        images: Match.Optional([String]),
        bedrooms: Match.Optional(Number),
        bathrooms: Match.Optional(Number),
        area: Match.Optional(Number)
      });
    } catch (error) {
      errors.push('Estructura de datos inválida');
      return { isValid: false, errors };
    }

    // Validaciones específicas
    if (!this.isInAllowedValues(data.type, ['venta', 'renta', 'socio', 'intercambio'])) {
      errors.push('Tipo de operación inválido');
    }

    if (!this.isValidLength(data.title, 5, 200)) {
      errors.push('El título debe tener entre 5 y 200 caracteres');
    }

    if (!this.isValidLength(data.description, 20, 2000)) {
      errors.push('La descripción debe tener entre 20 y 2000 caracteres');
    }

    if (!this.isInRange(data.price, 1, 999999999)) {
      errors.push('El precio debe estar entre 1 y 999,999,999');
    }

    if (!this.isInAllowedValues(data.location, ['norte', 'sur', 'este', 'oeste', 'centro'])) {
      errors.push('Ubicación inválida');
    }

    if (!this.isSafeText(data.title)) {
      errors.push('El título contiene caracteres no permitidos');
    }

    if (!this.isSafeText(data.description)) {
      errors.push('La descripción contiene caracteres no permitidos');
    }

    // Validar imágenes si existen
    if (data.images && Array.isArray(data.images)) {
      for (const image of data.images) {
        if (!this.isValidUrl(image)) {
          errors.push(`URL de imagen inválida: ${image}`);
        }
      }
    }

    // Validar campos opcionales
    if (data.bedrooms !== undefined && !this.isInRange(data.bedrooms, 0, 20)) {
      errors.push('El número de habitaciones debe estar entre 0 y 20');
    }

    if (data.bathrooms !== undefined && !this.isInRange(data.bathrooms, 0, 10)) {
      errors.push('El número de baños debe estar entre 0 y 10');
    }

    if (data.area !== undefined && !this.isInRange(data.area, 1, 10000)) {
      errors.push('El área debe estar entre 1 y 10,000 metros cuadrados');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  },

  // Validar datos de comentario
  validateCommentData(data) {
    const errors = [];

    try {
      check(data, {
        postId: String,
        text: String
      });
    } catch (error) {
      errors.push('Estructura de datos inválida');
      return { isValid: false, errors };
    }

    if (!this.isValidLength(data.text, 1, 1000)) {
      errors.push('El comentario debe tener entre 1 y 1000 caracteres');
    }

    if (!this.isSafeText(data.text)) {
      errors.push('El comentario contiene caracteres no permitidos');
    }

    if (!data.postId.trim()) {
      errors.push('ID de post inválido');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  },

  // Validar datos de notificación
  validateNotificationData(data) {
    const errors = [];

    try {
      check(data, {
        userId: String,
        title: String,
        message: String,
        type: Match.Optional(String),
        iconName: Match.Optional(String),
        data: Match.Optional(Object)
      });
    } catch (error) {
      errors.push('Estructura de datos inválida');
      return { isValid: false, errors };
    }

    if (!this.isValidLength(data.title, 1, 200)) {
      errors.push('El título debe tener entre 1 y 200 caracteres');
    }

    if (!this.isValidLength(data.message, 1, 1000)) {
      errors.push('El mensaje debe tener entre 1 y 1000 caracteres');
    }

    if (data.type && !this.isInAllowedValues(data.type, ['info', 'success', 'warning', 'error'])) {
      errors.push('Tipo de notificación inválido');
    }

    if (!this.isSafeText(data.title)) {
      errors.push('El título contiene caracteres no permitidos');
    }

    if (!this.isSafeText(data.message)) {
      errors.push('El mensaje contiene caracteres no permitidos');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  },

  // Validar filtros de búsqueda
  validateSearchFilters(filters) {
    const errors = [];

    if (filters.type && !this.isInAllowedValues(filters.type, ['venta', 'renta', 'socio', 'intercambio'])) {
      errors.push('Tipo de operación inválido en filtros');
    }

    if (filters.location && !this.isInAllowedValues(filters.location, ['norte', 'sur', 'este', 'oeste', 'centro'])) {
      errors.push('Ubicación inválida en filtros');
    }

    if (filters.minPrice && (!Number.isInteger(filters.minPrice) || filters.minPrice < 0)) {
      errors.push('Precio mínimo inválido');
    }

    if (filters.maxPrice && (!Number.isInteger(filters.maxPrice) || filters.maxPrice < 0)) {
      errors.push('Precio máximo inválido');
    }

    if (filters.minPrice && filters.maxPrice && filters.minPrice > filters.maxPrice) {
      errors.push('El precio mínimo no puede ser mayor al precio máximo');
    }

    if (filters.bedrooms && (!Number.isInteger(filters.bedrooms) || filters.bedrooms < 0)) {
      errors.push('Número de habitaciones inválido');
    }

    if (filters.bathrooms && (!Number.isInteger(filters.bathrooms) || filters.bathrooms < 0)) {
      errors.push('Número de baños inválido');
    }

    if (filters.searchText && !this.isSafeText(filters.searchText)) {
      errors.push('Texto de búsqueda contiene caracteres no permitidos');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }
};
