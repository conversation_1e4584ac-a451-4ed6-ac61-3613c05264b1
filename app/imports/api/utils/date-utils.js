// Utilidades para manejo de fechas
export const DateUtils = {
  // Formatear fecha para mostrar
  formatDate(date, locale = 'es-ES') {
    if (!date) return '';
    
    const dateObj = date instanceof Date ? date : new Date(date);
    
    return dateObj.toLocaleDateString(locale, {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  },

  // Formatear fecha y hora
  formatDateTime(date, locale = 'es-ES') {
    if (!date) return '';
    
    const dateObj = date instanceof Date ? date : new Date(date);
    
    return dateObj.toLocaleString(locale, {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  },

  // Obtener tiempo transcurrido desde una fecha
  getTimeAgo(date, locale = 'es') {
    if (!date) return '';
    
    const dateObj = date instanceof Date ? date : new Date(date);
    const now = new Date();
    const diff = now - dateObj;
    
    const seconds = Math.floor(diff / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);
    const weeks = Math.floor(days / 7);
    const months = Math.floor(days / 30);
    const years = Math.floor(days / 365);

    if (locale === 'es') {
      if (years > 0) return `${years} año${years > 1 ? 's' : ''} atrás`;
      if (months > 0) return `${months} mes${months > 1 ? 'es' : ''} atrás`;
      if (weeks > 0) return `${weeks} semana${weeks > 1 ? 's' : ''} atrás`;
      if (days > 0) return `${days} día${days > 1 ? 's' : ''} atrás`;
      if (hours > 0) return `${hours} hora${hours > 1 ? 's' : ''} atrás`;
      if (minutes > 0) return `${minutes} minuto${minutes > 1 ? 's' : ''} atrás`;
      return 'Hace un momento';
    } else {
      if (years > 0) return `${years} year${years > 1 ? 's' : ''} ago`;
      if (months > 0) return `${months} month${months > 1 ? 's' : ''} ago`;
      if (weeks > 0) return `${weeks} week${weeks > 1 ? 's' : ''} ago`;
      if (days > 0) return `${days} day${days > 1 ? 's' : ''} ago`;
      if (hours > 0) return `${hours} hour${hours > 1 ? 's' : ''} ago`;
      if (minutes > 0) return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
      return 'Just now';
    }
  },

  // Verificar si una fecha es hoy
  isToday(date) {
    if (!date) return false;
    
    const dateObj = date instanceof Date ? date : new Date(date);
    const today = new Date();
    
    return dateObj.toDateString() === today.toDateString();
  },

  // Verificar si una fecha es ayer
  isYesterday(date) {
    if (!date) return false;
    
    const dateObj = date instanceof Date ? date : new Date(date);
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    
    return dateObj.toDateString() === yesterday.toDateString();
  },

  // Verificar si una fecha es de esta semana
  isThisWeek(date) {
    if (!date) return false;
    
    const dateObj = date instanceof Date ? date : new Date(date);
    const now = new Date();
    
    // Obtener el inicio de la semana (lunes)
    const startOfWeek = new Date(now);
    const day = now.getDay();
    const diff = now.getDate() - day + (day === 0 ? -6 : 1);
    startOfWeek.setDate(diff);
    startOfWeek.setHours(0, 0, 0, 0);
    
    // Obtener el final de la semana (domingo)
    const endOfWeek = new Date(startOfWeek);
    endOfWeek.setDate(startOfWeek.getDate() + 6);
    endOfWeek.setHours(23, 59, 59, 999);
    
    return dateObj >= startOfWeek && dateObj <= endOfWeek;
  },

  // Obtener el inicio del día
  getStartOfDay(date) {
    const dateObj = date instanceof Date ? new Date(date) : new Date(date);
    dateObj.setHours(0, 0, 0, 0);
    return dateObj;
  },

  // Obtener el final del día
  getEndOfDay(date) {
    const dateObj = date instanceof Date ? new Date(date) : new Date(date);
    dateObj.setHours(23, 59, 59, 999);
    return dateObj;
  },

  // Agregar días a una fecha
  addDays(date, days) {
    const dateObj = date instanceof Date ? new Date(date) : new Date(date);
    dateObj.setDate(dateObj.getDate() + days);
    return dateObj;
  },

  // Restar días a una fecha
  subtractDays(date, days) {
    return this.addDays(date, -days);
  },

  // Obtener rango de fechas para filtros
  getDateRange(period) {
    const now = new Date();
    const ranges = {
      today: {
        start: this.getStartOfDay(now),
        end: this.getEndOfDay(now)
      },
      yesterday: {
        start: this.getStartOfDay(this.subtractDays(now, 1)),
        end: this.getEndOfDay(this.subtractDays(now, 1))
      },
      thisWeek: {
        start: (() => {
          const startOfWeek = new Date(now);
          const day = now.getDay();
          const diff = now.getDate() - day + (day === 0 ? -6 : 1);
          startOfWeek.setDate(diff);
          return this.getStartOfDay(startOfWeek);
        })(),
        end: this.getEndOfDay(now)
      },
      lastWeek: {
        start: (() => {
          const startOfLastWeek = new Date(now);
          const day = now.getDay();
          const diff = now.getDate() - day + (day === 0 ? -6 : 1) - 7;
          startOfLastWeek.setDate(diff);
          return this.getStartOfDay(startOfLastWeek);
        })(),
        end: (() => {
          const endOfLastWeek = new Date(now);
          const day = now.getDay();
          const diff = now.getDate() - day + (day === 0 ? -6 : 1) - 1;
          endOfLastWeek.setDate(diff);
          return this.getEndOfDay(endOfLastWeek);
        })()
      },
      thisMonth: {
        start: new Date(now.getFullYear(), now.getMonth(), 1),
        end: this.getEndOfDay(now)
      },
      lastMonth: {
        start: new Date(now.getFullYear(), now.getMonth() - 1, 1),
        end: new Date(now.getFullYear(), now.getMonth(), 0, 23, 59, 59, 999)
      },
      last30Days: {
        start: this.getStartOfDay(this.subtractDays(now, 30)),
        end: this.getEndOfDay(now)
      },
      last90Days: {
        start: this.getStartOfDay(this.subtractDays(now, 90)),
        end: this.getEndOfDay(now)
      }
    };

    return ranges[period] || ranges.today;
  },

  // Validar si una fecha es válida
  isValidDate(date) {
    if (!date) return false;
    const dateObj = date instanceof Date ? date : new Date(date);
    return !isNaN(dateObj.getTime());
  },

  // Comparar dos fechas
  compareDates(date1, date2) {
    const d1 = date1 instanceof Date ? date1 : new Date(date1);
    const d2 = date2 instanceof Date ? date2 : new Date(date2);
    
    if (d1 < d2) return -1;
    if (d1 > d2) return 1;
    return 0;
  },

  // Obtener diferencia en días entre dos fechas
  getDaysDifference(date1, date2) {
    const d1 = date1 instanceof Date ? date1 : new Date(date1);
    const d2 = date2 instanceof Date ? date2 : new Date(date2);
    
    const diffTime = Math.abs(d2 - d1);
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  }
};
