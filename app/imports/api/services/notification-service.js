// Servicio para manejo de notificaciones
import { Notifications } from '../collections/notifications';
import { Logger } from '../../startup/server/logging';

export const NotificationService = {
  // Crear una notificación
  async create(notificationData) {
    try {
      const notification = {
        userId: notificationData.userId,
        title: notificationData.title,
        message: notificationData.message,
        type: notificationData.type || 'info',
        iconName: notificationData.iconName,
        data: notificationData.data,
        read: false,
        createdAt: new Date(),
        source: notificationData.source || 'system'
      };

      const notificationId = await Notifications.insertAsync(notification);
      
      Logger.info('Notificación creada por servicio', { 
        notificationId, 
        userId: notificationData.userId 
      });
      
      return notificationId;
    } catch (error) {
      Logger.error('Error al crear notificación en servicio', error);
      throw error;
    }
  },

  // Notificar nuevo comentario en post
  async notifyNewComment(postAuthorId, commenterName, postTitle) {
    if (!postAuthorId) return;

    try {
      await this.create({
        userId: postAuthorId,
        title: 'Nuevo comentario',
        message: `${commenterName} ha comentado en tu publicación "${postTitle}"`,
        type: 'info',
        iconName: 'chatbubble',
        source: 'comment'
      });
    } catch (error) {
      Logger.error('Error al notificar nuevo comentario', error);
    }
  },

  // Notificar nuevo interés en post
  async notifyNewInterest(postAuthorId, interestedUserName, postTitle) {
    if (!postAuthorId) return;

    try {
      await this.create({
        userId: postAuthorId,
        title: 'Nuevo interés',
        message: `${interestedUserName} está interesado en tu publicación "${postTitle}"`,
        type: 'success',
        iconName: 'heart',
        source: 'interest'
      });
    } catch (error) {
      Logger.error('Error al notificar nuevo interés', error);
    }
  },

  // Notificar nueva propiedad que coincide con criterios
  async notifyMatchingProperty(userId, propertyTitle, criteria) {
    if (!userId) return;

    try {
      await this.create({
        userId: userId,
        title: 'Nueva propiedad disponible',
        message: `Se ha agregado "${propertyTitle}" que coincide con tus criterios de búsqueda`,
        type: 'info',
        iconName: 'home',
        data: { criteria },
        source: 'matching'
      });
    } catch (error) {
      Logger.error('Error al notificar propiedad coincidente', error);
    }
  },

  // Marcar notificaciones como leídas
  async markAsRead(notificationIds, userId) {
    try {
      const result = await Notifications.updateAsync(
        { 
          _id: { $in: notificationIds },
          userId: userId 
        },
        { 
          $set: { 
            read: true,
            readAt: new Date()
          } 
        },
        { multi: true }
      );

      Logger.info('Notificaciones marcadas como leídas', { 
        count: result, 
        userId 
      });

      return result;
    } catch (error) {
      Logger.error('Error al marcar notificaciones como leídas', error);
      throw error;
    }
  },

  // Limpiar notificaciones antiguas
  async cleanupOldNotifications(userId, daysOld = 30) {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - daysOld);

      const result = await Notifications.removeAsync({
        userId: userId,
        createdAt: { $lt: cutoffDate },
        read: true
      });

      Logger.info('Notificaciones antiguas eliminadas', { 
        count: result, 
        userId, 
        daysOld 
      });

      return result;
    } catch (error) {
      Logger.error('Error al limpiar notificaciones antiguas', error);
      throw error;
    }
  },

  // Obtener estadísticas de notificaciones
  async getStats(userId) {
    try {
      const total = await Notifications.find({ userId }).countAsync();
      const unread = await Notifications.find({ userId, read: false }).countAsync();
      const byType = await Notifications.rawCollection().aggregate([
        { $match: { userId } },
        { $group: { _id: '$type', count: { $sum: 1 } } }
      ]).toArray();

      return {
        total,
        unread,
        read: total - unread,
        byType: byType.reduce((acc, item) => {
          acc[item._id] = item.count;
          return acc;
        }, {})
      };
    } catch (error) {
      Logger.error('Error al obtener estadísticas de notificaciones', error);
      throw error;
    }
  }
};
