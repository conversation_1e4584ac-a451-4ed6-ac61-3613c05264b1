// Servicio para manejo de posts inmobiliarios
import { Meteor } from 'meteor/meteor';
import { PostsInmobiliarios } from '../collections/posts-inmobiliarios';
import { InteresadosPost } from '../collections/interesados-post';
import { ComentariosPost } from '../collections/comentarios-post';
import { NotificationService } from './notification-service';
import { Logger } from '../../startup/server/logging';

export const PostService = {
  // Crear un nuevo post
  async createPost(postData, authorId) {
    try {
      // Obtener información del autor
      const author = Meteor.users.findOne(authorId);
      if (!author) {
        throw new Meteor.Error('author-not-found', 'Autor no encontrado');
      }

      const post = {
        ...postData,
        author: {
          id: authorId,
          name: author.profile?.name || 'Usuario',
          avatar: author.profile?.avatar || '/default-avatar.png',
        },
        date: new Date(),
        interestedCount: 0,
        commentsCount: 0,
        createdAt: new Date()
      };

      const postId = await PostsInmobiliarios.insertAsync(post);
      
      Logger.info('Post creado por servicio', { postId, authorId });
      
      // Notificar a usuarios con criterios coincidentes (implementar lógica según necesidades)
      // await this.notifyMatchingUsers(post);
      
      return postId;
    } catch (error) {
      Logger.error('Error al crear post en servicio', error);
      throw error;
    }
  },

  // Agregar interés a un post
  async addInterest(postId, userId) {
    try {
      // Verificar si ya existe el interés
      const existingInterest = await InteresadosPost.findOneAsync({
        postId,
        userId
      });

      if (existingInterest) {
        return { success: false, message: 'Ya estás interesado en este post' };
      }

      // Agregar interés
      await InteresadosPost.insertAsync({
        postId,
        userId,
        date: new Date()
      });

      // Incrementar contador
      await PostsInmobiliarios.updateAsync(postId, {
        $inc: { interestedCount: 1 }
      });

      // Notificar al autor del post
      const post = await PostsInmobiliarios.findOneAsync(postId);
      if (post && post.author.id !== userId) {
        const user = Meteor.users.findOne(userId);
        await NotificationService.notifyNewInterest(
          post.author.id,
          user?.profile?.name || 'Un usuario',
          post.title
        );
      }

      Logger.info('Interés agregado por servicio', { postId, userId });
      
      return { success: true, message: 'Interés agregado correctamente' };
    } catch (error) {
      Logger.error('Error al agregar interés en servicio', error);
      throw error;
    }
  },

  // Remover interés de un post
  async removeInterest(postId, userId) {
    try {
      const interest = await InteresadosPost.findOneAsync({
        postId,
        userId
      });

      if (!interest) {
        return { success: false, message: 'No estás interesado en este post' };
      }

      // Remover interés
      await InteresadosPost.removeAsync(interest._id);

      // Decrementar contador
      await PostsInmobiliarios.updateAsync(postId, {
        $inc: { interestedCount: -1 }
      });

      Logger.info('Interés removido por servicio', { postId, userId });
      
      return { success: true, message: 'Interés removido correctamente' };
    } catch (error) {
      Logger.error('Error al remover interés en servicio', error);
      throw error;
    }
  },

  // Obtener posts con filtros avanzados
  async getFilteredPosts(filters, pagination) {
    try {
      const query = {};
      const options = {
        sort: { date: -1 },
        limit: pagination.limit || 10,
        skip: ((pagination.page || 1) - 1) * (pagination.limit || 10)
      };

      // Aplicar filtros
      if (filters.type) {
        query.type = filters.type;
      }

      if (filters.location) {
        query.location = filters.location;
      }

      if (filters.minPrice || filters.maxPrice) {
        query.price = {};
        if (filters.minPrice) query.price.$gte = filters.minPrice;
        if (filters.maxPrice) query.price.$lte = filters.maxPrice;
      }

      if (filters.bedrooms) {
        query.bedrooms = filters.bedrooms;
      }

      if (filters.bathrooms) {
        query.bathrooms = filters.bathrooms;
      }

      if (filters.searchText) {
        query.$or = [
          { title: { $regex: filters.searchText, $options: 'i' } },
          { description: { $regex: filters.searchText, $options: 'i' } }
        ];
      }

      const posts = await PostsInmobiliarios.find(query, options).fetchAsync();
      const total = await PostsInmobiliarios.find(query).countAsync();

      return {
        posts,
        total,
        page: pagination.page || 1,
        limit: pagination.limit || 10,
        hasMore: (options.skip + options.limit) < total
      };
    } catch (error) {
      Logger.error('Error al obtener posts filtrados', error);
      throw error;
    }
  },

  // Obtener estadísticas de un post
  async getPostStats(postId) {
    try {
      const post = await PostsInmobiliarios.findOneAsync(postId);
      if (!post) {
        throw new Meteor.Error('post-not-found', 'Post no encontrado');
      }

      const commentsCount = await ComentariosPost.find({ postId }).countAsync();
      const interestsCount = await InteresadosPost.find({ postId }).countAsync();

      // Actualizar contadores si están desactualizados
      if (post.commentsCount !== commentsCount || post.interestedCount !== interestsCount) {
        await PostsInmobiliarios.updateAsync(postId, {
          $set: {
            commentsCount,
            interestedCount: interestsCount
          }
        });
      }

      return {
        id: postId,
        commentsCount,
        interestsCount,
        views: post.views || 0, // Implementar sistema de vistas si es necesario
        createdAt: post.createdAt,
        updatedAt: post.updatedAt
      };
    } catch (error) {
      Logger.error('Error al obtener estadísticas del post', error);
      throw error;
    }
  },

  // Buscar posts similares
  async findSimilarPosts(postId, limit = 5) {
    try {
      const post = await PostsInmobiliarios.findOneAsync(postId);
      if (!post) {
        throw new Meteor.Error('post-not-found', 'Post no encontrado');
      }

      // Buscar posts similares basados en tipo, ubicación y rango de precio
      const priceRange = post.price * 0.2; // 20% de variación en precio
      
      const similarPosts = await PostsInmobiliarios.find({
        _id: { $ne: postId },
        type: post.type,
        location: post.location,
        price: {
          $gte: post.price - priceRange,
          $lte: post.price + priceRange
        }
      }, {
        limit,
        sort: { date: -1 }
      }).fetchAsync();

      return similarPosts;
    } catch (error) {
      Logger.error('Error al buscar posts similares', error);
      throw error;
    }
  },

  // Validar datos del post
  validatePostData(postData) {
    const errors = [];

    if (!postData.title || postData.title.trim().length < 5) {
      errors.push('El título debe tener al menos 5 caracteres');
    }

    if (!postData.description || postData.description.trim().length < 20) {
      errors.push('La descripción debe tener al menos 20 caracteres');
    }

    if (!postData.price || postData.price <= 0) {
      errors.push('El precio debe ser mayor a 0');
    }

    if (!postData.type || !['venta', 'renta', 'socio', 'intercambio'].includes(postData.type)) {
      errors.push('Tipo de operación inválido');
    }

    if (!postData.location || !['norte', 'sur', 'este', 'oeste', 'centro'].includes(postData.location)) {
      errors.push('Ubicación inválida');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }
};
