// Tests para utilidades de validación
import { assert } from 'chai';
import { ValidationUtils } from '../utils/validation-utils';

describe('ValidationUtils', function () {
  describe('isValidEmail', function () {
    it('should validate correct email addresses', function () {
      assert.isTrue(ValidationUtils.isValidEmail('<EMAIL>'));
      assert.isTrue(ValidationUtils.isValidEmail('<EMAIL>'));
      assert.isTrue(ValidationUtils.isValidEmail('<EMAIL>'));
    });

    it('should reject invalid email addresses', function () {
      assert.isFalse(ValidationUtils.isValidEmail('invalid-email'));
      assert.isFalse(ValidationUtils.isValidEmail('@example.com'));
      assert.isFalse(ValidationUtils.isValidEmail('test@'));
      assert.isFalse(ValidationUtils.isValidEmail(''));
    });
  });

  describe('isValidUrl', function () {
    it('should validate correct URLs', function () {
      assert.isTrue(ValidationUtils.isValidUrl('https://example.com'));
      assert.isTrue(ValidationUtils.isValidUrl('http://test.org/path'));
      assert.isTrue(ValidationUtils.isValidUrl('https://subdomain.example.com/path?query=1'));
    });

    it('should reject invalid URLs', function () {
      assert.isFalse(ValidationUtils.isValidUrl('not-a-url'));
      assert.isFalse(ValidationUtils.isValidUrl('ftp://invalid'));
      assert.isFalse(ValidationUtils.isValidUrl(''));
    });
  });

  describe('isSafeText', function () {
    it('should allow safe text', function () {
      assert.isTrue(ValidationUtils.isSafeText('This is safe text'));
      assert.isTrue(ValidationUtils.isSafeText('Text with números 123'));
      assert.isTrue(ValidationUtils.isSafeText('Text with symbols: !@#$%^&*()'));
    });

    it('should reject dangerous text', function () {
      assert.isFalse(ValidationUtils.isSafeText('<script>alert("xss")</script>'));
      assert.isFalse(ValidationUtils.isSafeText('javascript:alert("xss")'));
      assert.isFalse(ValidationUtils.isSafeText('data:text/html,<script>alert("xss")</script>'));
      assert.isFalse(ValidationUtils.isSafeText('vbscript:msgbox("xss")'));
    });
  });

  describe('sanitizeText', function () {
    it('should remove dangerous content', function () {
      const dangerous = '<script>alert("xss")</script>Hello World';
      const sanitized = ValidationUtils.sanitizeText(dangerous);
      assert.equal(sanitized, 'Hello World');
    });

    it('should remove javascript: protocols', function () {
      const dangerous = 'javascript:alert("xss") Hello';
      const sanitized = ValidationUtils.sanitizeText(dangerous);
      assert.equal(sanitized, 'alert("xss") Hello');
    });

    it('should handle non-string input', function () {
      assert.equal(ValidationUtils.sanitizeText(null), '');
      assert.equal(ValidationUtils.sanitizeText(undefined), '');
      assert.equal(ValidationUtils.sanitizeText(123), '');
    });
  });

  describe('isInRange', function () {
    it('should validate numbers in range', function () {
      assert.isTrue(ValidationUtils.isInRange(5, 1, 10));
      assert.isTrue(ValidationUtils.isInRange(1, 1, 10));
      assert.isTrue(ValidationUtils.isInRange(10, 1, 10));
    });

    it('should reject numbers out of range', function () {
      assert.isFalse(ValidationUtils.isInRange(0, 1, 10));
      assert.isFalse(ValidationUtils.isInRange(11, 1, 10));
      assert.isFalse(ValidationUtils.isInRange('5', 1, 10)); // string
    });
  });

  describe('validatePostData', function () {
    it('should validate correct post data', function () {
      const validPost = {
        type: 'venta',
        title: 'Casa hermosa en zona residencial',
        description: 'Esta es una descripción detallada de la propiedad que tiene más de 20 caracteres',
        price: 1000000,
        location: 'norte',
        images: ['https://example.com/image1.jpg', 'https://example.com/image2.jpg'],
        bedrooms: 3,
        bathrooms: 2,
        area: 150
      };

      const result = ValidationUtils.validatePostData(validPost);
      assert.isTrue(result.isValid);
      assert.equal(result.errors.length, 0);
    });

    it('should reject invalid post data', function () {
      const invalidPost = {
        type: 'invalid-type',
        title: 'Short', // Muy corto
        description: 'Short desc', // Muy corto
        price: -1000, // Precio negativo
        location: 'invalid-location',
        images: ['not-a-url'], // URL inválida
        bedrooms: 25, // Fuera de rango
        bathrooms: 15, // Fuera de rango
        area: 15000 // Fuera de rango
      };

      const result = ValidationUtils.validatePostData(invalidPost);
      assert.isFalse(result.isValid);
      assert.isAbove(result.errors.length, 0);
    });

    it('should handle missing required fields', function () {
      const incompletePost = {
        type: 'venta'
        // Faltan campos requeridos
      };

      const result = ValidationUtils.validatePostData(incompletePost);
      assert.isFalse(result.isValid);
      assert.include(result.errors[0], 'Estructura de datos inválida');
    });
  });

  describe('validateCommentData', function () {
    it('should validate correct comment data', function () {
      const validComment = {
        postId: 'valid-post-id',
        text: 'Este es un comentario válido con suficiente contenido'
      };

      const result = ValidationUtils.validateCommentData(validComment);
      assert.isTrue(result.isValid);
      assert.equal(result.errors.length, 0);
    });

    it('should reject invalid comment data', function () {
      const invalidComment = {
        postId: '', // ID vacío
        text: '' // Texto vacío
      };

      const result = ValidationUtils.validateCommentData(invalidComment);
      assert.isFalse(result.isValid);
      assert.isAbove(result.errors.length, 0);
    });

    it('should reject dangerous content in comments', function () {
      const dangerousComment = {
        postId: 'valid-post-id',
        text: '<script>alert("xss")</script>Comentario peligroso'
      };

      const result = ValidationUtils.validateCommentData(dangerousComment);
      assert.isFalse(result.isValid);
      assert.include(result.errors.join(' '), 'caracteres no permitidos');
    });
  });

  describe('validateSearchFilters', function () {
    it('should validate correct search filters', function () {
      const validFilters = {
        type: 'venta',
        location: 'norte',
        minPrice: 500000,
        maxPrice: 2000000,
        bedrooms: 3,
        bathrooms: 2,
        searchText: 'casa hermosa'
      };

      const result = ValidationUtils.validateSearchFilters(validFilters);
      assert.isTrue(result.isValid);
      assert.equal(result.errors.length, 0);
    });

    it('should reject invalid search filters', function () {
      const invalidFilters = {
        type: 'invalid-type',
        location: 'invalid-location',
        minPrice: -1000,
        maxPrice: -500,
        bedrooms: -1,
        bathrooms: -1,
        searchText: '<script>alert("xss")</script>'
      };

      const result = ValidationUtils.validateSearchFilters(invalidFilters);
      assert.isFalse(result.isValid);
      assert.isAbove(result.errors.length, 0);
    });

    it('should reject when minPrice > maxPrice', function () {
      const invalidFilters = {
        minPrice: 2000000,
        maxPrice: 1000000
      };

      const result = ValidationUtils.validateSearchFilters(invalidFilters);
      assert.isFalse(result.isValid);
      assert.include(result.errors.join(' '), 'mínimo no puede ser mayor');
    });
  });
});
