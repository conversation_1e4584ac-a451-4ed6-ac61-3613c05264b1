// Tests para métodos de posts inmobiliarios
import { Meteor } from 'meteor/meteor';
import { Random } from 'meteor/random';
import { assert } from 'chai';
import { PostsInmobiliarios } from '../collections/posts-inmobiliarios';
import { InteresadosPost } from '../collections/interesados-post';

if (Meteor.isServer) {
  describe('Posts Methods', function () {
    let userId;
    let postId;

    beforeEach(function () {
      // Limpiar colecciones
      PostsInmobiliarios.remove({});
      InteresadosPost.remove({});
      
      // Crear usuario de prueba
      userId = Random.id();
    });

    describe('posts.create', function () {
      it('should create a new post with valid data', async function () {
        const postData = {
          type: 'venta',
          title: 'Casa de prueba',
          description: 'Esta es una descripción de prueba para la casa',
          price: 1000000,
          location: 'norte'
        };

        // Simular contexto de método
        const context = { userId };
        
        // Mock del usuario
        const originalFindOne = Meteor.users.findOne;
        Meteor.users.findOne = () => ({
          profile: { name: 'Usuario Test', avatar: '/test-avatar.jpg' }
        });

        try {
          const result = await Meteor.call('posts.create', postData);
          
          assert.isString(result);
          
          const post = PostsInmobiliarios.findOne(result);
          assert.isObject(post);
          assert.equal(post.title, postData.title);
          assert.equal(post.author.id, userId);
          assert.equal(post.interestedCount, 0);
          assert.equal(post.commentsCount, 0);
        } finally {
          // Restaurar método original
          Meteor.users.findOne = originalFindOne;
        }
      });

      it('should throw error when user is not authenticated', async function () {
        const postData = {
          type: 'venta',
          title: 'Casa de prueba',
          description: 'Esta es una descripción de prueba para la casa',
          price: 1000000,
          location: 'norte'
        };

        try {
          await Meteor.call('posts.create', postData);
          assert.fail('Should have thrown an error');
        } catch (error) {
          assert.equal(error.error, 'not-authorized');
        }
      });

      it('should validate required fields', async function () {
        const invalidPostData = {
          type: 'venta',
          title: 'Test', // Muy corto
          description: 'Corto', // Muy corto
          price: -1000, // Precio negativo
          location: 'invalid' // Ubicación inválida
        };

        const context = { userId };

        try {
          await Meteor.call('posts.create', invalidPostData);
          assert.fail('Should have thrown validation error');
        } catch (error) {
          assert.include(error.message, 'Match failed');
        }
      });
    });

    describe('posts.toggleInterest', function () {
      beforeEach(async function () {
        // Crear un post de prueba
        postId = PostsInmobiliarios.insert({
          type: 'venta',
          title: 'Casa de prueba',
          description: 'Esta es una descripción de prueba para la casa',
          price: 1000000,
          location: 'norte',
          author: {
            id: Random.id(),
            name: 'Otro Usuario',
            avatar: '/avatar.jpg'
          },
          date: new Date(),
          interestedCount: 0,
          commentsCount: 0
        });
      });

      it('should add interest when user is not interested', async function () {
        const context = { userId };
        
        const result = await Meteor.call('posts.toggleInterest', postId);
        
        assert.isTrue(result);
        
        const interest = InteresadosPost.findOne({ postId, userId });
        assert.isObject(interest);
        
        const post = PostsInmobiliarios.findOne(postId);
        assert.equal(post.interestedCount, 1);
      });

      it('should remove interest when user is already interested', async function () {
        // Primero agregar interés
        InteresadosPost.insert({
          postId,
          userId,
          date: new Date()
        });
        
        PostsInmobiliarios.update(postId, {
          $inc: { interestedCount: 1 }
        });

        const context = { userId };
        
        const result = await Meteor.call('posts.toggleInterest', postId);
        
        assert.isFalse(result);
        
        const interest = InteresadosPost.findOne({ postId, userId });
        assert.isUndefined(interest);
        
        const post = PostsInmobiliarios.findOne(postId);
        assert.equal(post.interestedCount, 0);
      });

      it('should throw error when user is not authenticated', async function () {
        try {
          await Meteor.call('posts.toggleInterest', postId);
          assert.fail('Should have thrown an error');
        } catch (error) {
          assert.equal(error.error, 'not-authorized');
        }
      });
    });

    describe('posts.getTotalCount', function () {
      beforeEach(function () {
        // Crear algunos posts de prueba
        PostsInmobiliarios.insert({
          type: 'venta',
          title: 'Casa Norte',
          description: 'Casa en zona norte',
          price: 1000000,
          location: 'norte',
          author: { id: Random.id(), name: 'User1' },
          date: new Date()
        });

        PostsInmobiliarios.insert({
          type: 'renta',
          title: 'Departamento Sur',
          description: 'Departamento en zona sur',
          price: 500000,
          location: 'sur',
          author: { id: Random.id(), name: 'User2' },
          date: new Date()
        });

        PostsInmobiliarios.insert({
          type: 'venta',
          title: 'Casa Norte 2',
          description: 'Otra casa en zona norte',
          price: 1500000,
          location: 'norte',
          author: { id: Random.id(), name: 'User3' },
          date: new Date()
        });
      });

      it('should return total count without filters', async function () {
        const count = await Meteor.call('posts.getTotalCount');
        assert.equal(count, 3);
      });

      it('should return filtered count by type', async function () {
        const count = await Meteor.call('posts.getTotalCount', { type: 'venta' });
        assert.equal(count, 2);
      });

      it('should return filtered count by location', async function () {
        const count = await Meteor.call('posts.getTotalCount', { location: 'norte' });
        assert.equal(count, 2);
      });

      it('should return filtered count by max price', async function () {
        const count = await Meteor.call('posts.getTotalCount', { maxPrice: 1000000 });
        assert.equal(count, 2);
      });

      it('should return filtered count with multiple filters', async function () {
        const count = await Meteor.call('posts.getTotalCount', { 
          type: 'venta', 
          location: 'norte' 
        });
        assert.equal(count, 2);
      });
    });
  });
}
