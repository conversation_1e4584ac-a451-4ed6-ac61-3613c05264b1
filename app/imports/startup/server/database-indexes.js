// Configuración de índices de base de datos
import { Meteor } from 'meteor/meteor';
import { PostsInmobiliarios } from '../../api/collections/posts-inmobiliarios';
import { ComentariosPost } from '../../api/collections/comentarios-post';
import { Notifications } from '../../api/collections/notifications';
import { InteresadosPost } from '../../api/collections/interesados-post';

if (Meteor.isServer) {
  Meteor.startup(() => {
    console.log('📊 Creating database indexes...');

    // Índices para PostsInmobiliarios
    PostsInmobiliarios.createIndex({ type: 1 });
    PostsInmobiliarios.createIndex({ location: 1 });
    PostsInmobiliarios.createIndex({ price: 1 });
    PostsInmobiliarios.createIndex({ date: -1 });
    PostsInmobiliarios.createIndex({ 'author.id': 1 });
    PostsInmobiliarios.createIndex({ 
      type: 1, 
      location: 1, 
      price: 1 
    }); // Índice compuesto para filtros

    // Índices para ComentariosPost
    ComentariosPost.createIndex({ postId: 1 });
    ComentariosPost.createIndex({ 'author.id': 1 });
    ComentariosPost.createIndex({ date: -1 });

    // Índices para Notifications
    Notifications.createIndex({ userId: 1 });
    Notifications.createIndex({ read: 1 });
    Notifications.createIndex({ createdAt: -1 });
    Notifications.createIndex({ 
      userId: 1, 
      read: 1, 
      createdAt: -1 
    }); // Índice compuesto para consultas de notificaciones

    // Índices para InteresadosPost
    InteresadosPost.createIndex({ postId: 1 });
    InteresadosPost.createIndex({ userId: 1 });
    InteresadosPost.createIndex({ 
      postId: 1, 
      userId: 1 
    }, { unique: true }); // Evitar duplicados

    console.log('✅ Database indexes created successfully');
  });
}
