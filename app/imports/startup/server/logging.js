// Configuración de logging
import { Meteor } from 'meteor/meteor';

// Configurar logging personalizado
const Logger = {
  info: (message, data = {}) => {
    console.log(`ℹ️  [INFO] ${new Date().toISOString()} - ${message}`, data);
  },
  
  warn: (message, data = {}) => {
    console.warn(`⚠️  [WARN] ${new Date().toISOString()} - ${message}`, data);
  },
  
  error: (message, error = {}) => {
    console.error(`❌ [ERROR] ${new Date().toISOString()} - ${message}`, error);
  },
  
  debug: (message, data = {}) => {
    if (process.env.NODE_ENV === 'development') {
      console.log(`🐛 [DEBUG] ${new Date().toISOString()} - ${message}`, data);
    }
  }
};

// Exportar logger para uso global
export { Logger };

if (Meteor.isServer) {
  Meteor.startup(() => {
    console.log('📝 Logging system initialized');
    
    // Configurar manejo global de errores no capturadas
    process.on('uncaughtException', (error) => {
      Logger.error('Uncaught Exception:', error);
    });

    process.on('unhandledRejection', (reason, promise) => {
      Logger.error('Unhandled Rejection at:', { promise, reason });
    });

    // Log de inicio del servidor
    Logger.info('MulbinComponents Backend Server started', {
      nodeVersion: process.version,
      meteorVersion: Meteor.release,
      environment: process.env.NODE_ENV,
      port: process.env.PORT
    });
  });
}
