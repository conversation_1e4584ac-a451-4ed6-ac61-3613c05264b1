// Configuración de variables de entorno
import { Meteor } from 'meteor/meteor';

if (Meteor.isServer) {
  Meteor.startup(() => {
    console.log('🌍 Loading environment configuration...');

    // Validar variables de entorno requeridas
    const requiredEnvVars = [
      'MONGO_URL',
      'ROOT_URL'
    ];

    const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);
    
    if (missingVars.length > 0) {
      console.warn(`⚠️  Missing environment variables: ${missingVars.join(', ')}`);
    }

    // Configurar variables por defecto
    process.env.NODE_ENV = process.env.NODE_ENV || 'development';
    process.env.PORT = process.env.PORT || '3000';

    // Configuraciones específicas por ambiente
    if (Meteor.isProduction) {
      console.log('🚀 Running in PRODUCTION mode');
      // Configuraciones de producción
    } else {
      console.log('🔧 Running in DEVELOPMENT mode');
      // Configuraciones de desarrollo
    }

    console.log('✅ Environment configuration loaded');
  });
}
