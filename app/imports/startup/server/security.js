// Configuración de seguridad del servidor
import { Meteor } from 'meteor/meteor';
import { DDPRateLimiter } from 'meteor/ddp-rate-limiter';

if (Meteor.isServer) {
  Meteor.startup(() => {
    console.log('🔒 Configuring server security...');

    // Configurar rate limiting para métodos
    const methodRules = [
      {
        type: 'method',
        name: 'crearPostInmobiliario',
        connectionId: () => true,
      },
      {
        type: 'method',
        name: 'agregarComentarioPost',
        connectionId: () => true,
      },
      {
        type: 'method',
        name: 'toggleInteresadoPost',
        connectionId: () => true,
      }
    ];

    // Aplicar límites de velocidad
    methodRules.forEach(rule => {
      DDPRateLimiter.addRule(rule, 5, 60000); // 5 llamadas por minuto
    });

    // Rate limiting para publicaciones
    const publicationRules = [
      {
        type: 'subscription',
        name: 'postsInmobiliarios',
        connectionId: () => true,
      },
      {
        type: 'subscription',
        name: 'comentariosPost',
        connectionId: () => true,
      },
      {
        type: 'subscription',
        name: 'userNotifications',
        connectionId: () => true,
      }
    ];

    publicationRules.forEach(rule => {
      DDPRateLimiter.addRule(rule, 10, 60000); // 10 suscripciones por minuto
    });

    // Configurar CORS si es necesario
    if (process.env.NODE_ENV === 'development') {
      // En desarrollo, permitir CORS más permisivo
      console.log('🌐 CORS configured for development');
    }

    console.log('✅ Server security configured');
  });
}
