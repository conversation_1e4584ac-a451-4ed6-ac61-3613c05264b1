module.exports = {
  env: {
    browser: true,
    es2021: true,
    node: true,
    meteor: true,
    mocha: true
  },
  extends: [
    'eslint:recommended'
  ],
  parserOptions: {
    ecmaVersion: 'latest',
    sourceType: 'module'
  },
  globals: {
    // Meteor globals
    Meteor: 'readonly',
    Mongo: 'readonly',
    Random: 'readonly',
    check: 'readonly',
    Match: 'readonly',
    
    // Test globals
    describe: 'readonly',
    it: 'readonly',
    beforeEach: 'readonly',
    afterEach: 'readonly',
    before: 'readonly',
    after: 'readonly'
  },
  rules: {
    // Posibles errores
    'no-console': 'off', // Permitir console.log en desarrollo
    'no-unused-vars': ['error', { 'argsIgnorePattern': '^_' }],
    'no-undef': 'error',
    
    // Mejores prácticas
    'eqeqeq': 'error',
    'no-eval': 'error',
    'no-implied-eval': 'error',
    'no-new-func': 'error',
    'no-script-url': 'error',
    
    // Variables
    'no-use-before-define': ['error', { 'functions': false }],
    
    // Estilo de código
    'indent': ['error', 2],
    'quotes': ['error', 'single', { 'allowTemplateLiterals': true }],
    'semi': ['error', 'always'],
    'comma-dangle': ['error', 'never'],
    'no-trailing-spaces': 'error',
    'eol-last': 'error',
    
    // ES6+
    'prefer-const': 'error',
    'no-var': 'error',
    'arrow-spacing': 'error',
    'template-curly-spacing': 'error'
  },
  overrides: [
    {
      files: ['**/*.test.js', '**/*.tests.js'],
      rules: {
        'no-unused-expressions': 'off' // Para chai assertions
      }
    }
  ]
};
