import assert from "assert";

// Importar tests de la API
import "../imports/api/tests/posts-methods.tests.js";
import "../imports/api/tests/validation-utils.tests.js";

describe("MulbinComponents Backend", function () {
  it("package.json has correct name", async function () {
    const { name } = await import("../package.json");
    assert.strictEqual(name, "app");
  });

  if (Meteor.isClient) {
    it("client is not server", function () {
      assert.strictEqual(Meteor.isServer, false);
    });
  }

  if (Meteor.isServer) {
    it("server is not client", function () {
      assert.strictEqual(Meteor.isClient, false);
    });

    it("should have collections defined", function () {
      const { PostsInmobiliarios } = require("../imports/api/collections/posts-inmobiliarios");
      const { ComentariosPost } = require("../imports/api/collections/comentarios-post");
      const { Notifications } = require("../imports/api/collections/notifications");
      const { InteresadosPost } = require("../imports/api/collections/interesados-post");

      assert.ok(PostsInmobiliarios);
      assert.ok(ComentariosPost);
      assert.ok(Notifications);
      assert.ok(InteresadosPost);
    });

    it("should have methods defined", function () {
      // Verificar que los métodos están registrados
      const methodNames = [
        'posts.create',
        'posts.toggleInterest',
        'posts.getTotalCount',
        'comments.create',
        'notifications.markAsRead',
        'demo.generateNotifications'
      ];

      methodNames.forEach(methodName => {
        assert.ok(Meteor.server.method_handlers[methodName], `Method ${methodName} should be defined`);
      });
    });
  }
});
