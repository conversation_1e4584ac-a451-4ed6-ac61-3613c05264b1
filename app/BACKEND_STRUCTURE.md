# MulbinComponents - Estructura del Backend

Este documento describe la estructura modular y escalable del backend de MulbinComponents, un proyecto Meteor 3 diseñado específicamente para funcionar como backend únicamente, con frontend desacoplado que se conecta vía DDP externo.

## 📁 Estructura del Proyecto

```
app/
├── imports/
│   ├── api/                     # Lógica del servidor
│   │   ├── collections/         # Definiciones de colecciones
│   │   │   ├── index.js
│   │   │   ├── posts-inmobiliarios.js
│   │   │   ├── comentarios-post.js
│   │   │   ├── notifications.js
│   │   │   └── interesados-post.js
│   │   ├── methods/            # Métodos Meteor organizados por módulo
│   │   │   ├── index.js
│   │   │   ├── posts-inmobiliarios-methods.js
│   │   │   ├── comentarios-methods.js
│   │   │   ├── notifications-methods.js
│   │   │   └── demo-data-methods.js
│   │   ├── publications/       # Publicaciones organizadas
│   │   │   ├── index.js
│   │   │   ├── posts-inmobiliarios-publications.js
│   │   │   ├── comentarios-publications.js
│   │   │   └── notifications-publications.js
│   │   ├── schemas/            # Esquemas de validación
│   │   │   ├── post-inmobiliario-schema.js
│   │   │   ├── comentario-schema.js
│   │   │   ├── notification-schema.js
│   │   │   └── interesado-schema.js
│   │   ├── services/           # Servicios de negocio
│   │   │   ├── notification-service.js
│   │   │   └── post-service.js
│   │   ├── utils/              # Utilidades compartidas
│   │   │   ├── validation-utils.js
│   │   │   └── date-utils.js
│   │   ├── tests/              # Tests unitarios
│   │   │   ├── posts-methods.tests.js
│   │   │   └── validation-utils.tests.js
│   │   ├── collections.js      # Punto de entrada para colecciones
│   │   ├── methods.js          # Punto de entrada para métodos
│   │   └── publications.js     # Punto de entrada para publicaciones
│   ├── startup/                # Configuración de inicio
│   │   └── server/             # Configuración del servidor
│   │       ├── index.js
│   │       ├── database-indexes.js
│   │       ├── security.js
│   │       ├── environment.js
│   │       └── logging.js
│   └── lib/                    # Librerías compartidas
├── server/                     # Punto de entrada del servidor
│   └── main.js
├── tests/                      # Tests de integración
└── private/                    # Archivos privados del servidor
```

## 🏗️ Arquitectura

### Principios de Diseño

1. **Modularidad**: Cada funcionalidad está separada en módulos independientes
2. **Escalabilidad**: Estructura preparada para crecer sin problemas
3. **Mantenibilidad**: Código organizado y fácil de mantener
4. **Testabilidad**: Cada módulo puede ser probado independientemente
5. **Seguridad**: Validaciones y controles de seguridad en cada capa

### Capas de la Aplicación

1. **Colecciones**: Definición de esquemas de datos y reglas de negocio
2. **Métodos**: Lógica de negocio y operaciones del servidor
3. **Publicaciones**: Control de datos enviados al cliente
4. **Servicios**: Lógica de negocio reutilizable
5. **Utilidades**: Funciones auxiliares y validaciones

## 📊 Colecciones

### PostsInmobiliarios
Almacena las publicaciones inmobiliarias.

**Campos principales:**
- `type`: Tipo de operación (venta, renta, socio, intercambio)
- `title`: Título del post
- `description`: Descripción detallada
- `price`: Precio
- `location`: Ubicación (norte, sur, este, oeste, centro)
- `author`: Información del autor
- `images`: Array de URLs de imágenes
- `interestedCount`: Contador de interesados
- `commentsCount`: Contador de comentarios

### ComentariosPost
Almacena los comentarios en los posts.

**Campos principales:**
- `postId`: ID del post al que pertenece
- `author`: Información del autor del comentario
- `text`: Contenido del comentario
- `date`: Fecha de creación

### Notifications
Sistema de notificaciones para usuarios.

**Campos principales:**
- `userId`: ID del usuario destinatario
- `title`: Título de la notificación
- `message`: Mensaje
- `type`: Tipo (info, success, warning, error)
- `read`: Estado de lectura
- `iconName`: Icono asociado

### InteresadosPost
Relación entre usuarios y posts de interés.

**Campos principales:**
- `postId`: ID del post
- `userId`: ID del usuario interesado
- `date`: Fecha de interés

## 🔧 Métodos Principales

### Posts
- `posts.create`: Crear nuevo post
- `posts.update`: Actualizar post existente
- `posts.remove`: Eliminar post
- `posts.toggleInterest`: Marcar/desmarcar interés
- `posts.getTotalCount`: Obtener conteo total con filtros

### Comentarios
- `comments.create`: Crear comentario
- `comments.update`: Actualizar comentario
- `comments.remove`: Eliminar comentario
- `comments.getByPost`: Obtener comentarios paginados

### Notificaciones
- `notifications.markAsRead`: Marcar como leída
- `notifications.markAllAsRead`: Marcar todas como leídas
- `notifications.remove`: Eliminar notificación
- `notifications.getUnreadCount`: Obtener conteo no leídas

## 📡 Publicaciones

### Posts
- `postsInmobiliarios`: Posts con filtros y paginación
- `postInmobiliario`: Post específico
- `myPosts`: Posts del usuario actual
- `interestedPosts`: Posts de interés del usuario

### Comentarios
- `comentariosPost`: Comentarios de un post
- `myComments`: Comentarios del usuario
- `commentsOnMyPosts`: Comentarios en posts del usuario

### Notificaciones
- `userNotifications`: Notificaciones del usuario
- `unreadNotifications`: Notificaciones no leídas
- `notificationsPaginated`: Notificaciones paginadas

## 🛡️ Seguridad

### Rate Limiting
- Métodos: 5 llamadas por minuto
- Publicaciones: 10 suscripciones por minuto

### Validación
- Validación de entrada en todos los métodos
- Sanitización de texto para prevenir XSS
- Verificación de permisos en operaciones

### Autenticación
- Verificación de usuario autenticado en operaciones sensibles
- Control de permisos por recurso

## 🧪 Testing

### Tests Unitarios
- Métodos de posts: `posts-methods.tests.js`
- Utilidades de validación: `validation-utils.tests.js`

### Ejecutar Tests
```bash
# Tests unitarios
meteor test --driver-package meteortesting:mocha

# Tests de aplicación completa
meteor test --full-app --driver-package meteortesting:mocha
```

## 🚀 Desarrollo

### Instalación de Dependencias
```bash
cd app
meteor npm install
```

### Ejecutar en Desarrollo
```bash
# Con Docker
docker compose up meteor-dev

# Sin Docker
cd app
meteor run
```

### Variables de Entorno
- `MONGO_URL`: URL de conexión a MongoDB
- `ROOT_URL`: URL raíz de la aplicación
- `NODE_ENV`: Entorno (development/production)
- `PORT`: Puerto del servidor (default: 3000)

## 📝 Logging

Sistema de logging integrado con diferentes niveles:
- `Logger.info()`: Información general
- `Logger.warn()`: Advertencias
- `Logger.error()`: Errores
- `Logger.debug()`: Debug (solo en desarrollo)

## 🔄 Servicios

### NotificationService
Servicio para manejo centralizado de notificaciones:
- Crear notificaciones
- Notificar eventos automáticamente
- Limpiar notificaciones antiguas

### PostService
Servicio para lógica de negocio de posts:
- Crear posts con validaciones
- Manejo de intereses
- Búsquedas avanzadas
- Estadísticas

## 📈 Monitoreo

### Índices de Base de Datos
Índices optimizados para consultas frecuentes:
- Posts: tipo, ubicación, precio, fecha
- Comentarios: postId, autor, fecha
- Notificaciones: userId, leído, fecha

### Métricas
- Contadores automáticos de comentarios e intereses
- Estadísticas de posts y notificaciones
- Logs de operaciones importantes

## 🔧 Configuración de Producción

### Optimizaciones
- Índices de base de datos optimizados
- Rate limiting configurado
- Logging de errores
- Validaciones estrictas

### Deployment
El proyecto está configurado para deployment en contenedores Docker con Rancher en producción.

## 📚 Próximos Pasos

1. Implementar autenticación y autorización más robusta
2. Agregar sistema de roles y permisos
3. Implementar cache para consultas frecuentes
4. Agregar métricas y monitoreo avanzado
5. Implementar sistema de backup automático
