# Ejemplo de variables de entorno para MulbinComponents Backend

# Base de datos MongoDB
MONGO_URL=**************************************************************************

# URL raíz de la aplicación
ROOT_URL=http://localhost:3000

# Puerto del servidor
PORT=3000

# Entorno de ejecución
NODE_ENV=development

# Configuración de MongoDB para Docker Compose
MONGO_INITDB_ROOT_USERNAME=meteoruser
MONGO_INITDB_ROOT_PASSWORD=meteorpassword
MONGO_DB_NAME=meteorDev

# Configuración de seguridad (opcional)
# JWT_SECRET=your-jwt-secret-here
# ENCRYPTION_KEY=your-encryption-key-here

# Configuración de email (para notificaciones futuras)
# MAIL_URL=smtp://username:<EMAIL>:587

# Configuración de almacenamiento de archivos (futuro)
# AWS_ACCESS_KEY_ID=your-aws-access-key
# AWS_SECRET_ACCESS_KEY=your-aws-secret-key
# AWS_S3_BUCKET=your-s3-bucket-name
# AWS_REGION=us-east-1

# Configuración de logging
LOG_LEVEL=info

# Configuración de rate limiting
RATE_LIMIT_ENABLED=true
RATE_LIMIT_METHODS_PER_MINUTE=5
RATE_LIMIT_PUBLICATIONS_PER_MINUTE=10

# Configuración de desarrollo
METEOR_ALLOW_SUPERUSER=true
