// Script de inicialización de datos para desarrollo
// Este archivo puede ser usado para poblar la base de datos con datos de prueba

const initDevData = {
  // Usuarios de ejemplo
  users: [
    {
      _id: 'dev-user-1',
      profile: {
        name: '<PERSON>',
        avatar: 'https://randomuser.me/api/portraits/men/34.jpg',
        email: '<EMAIL>'
      }
    },
    {
      _id: 'dev-user-2', 
      profile: {
        name: '<PERSON>',
        avatar: 'https://randomuser.me/api/portraits/women/68.jpg',
        email: '<EMAIL>'
      }
    },
    {
      _id: 'dev-user-3',
      profile: {
        name: '<PERSON>', 
        avatar: 'https://randomuser.me/api/portraits/men/45.jpg',
        email: '<EMAIL>'
      }
    }
  ],

  // Posts de ejemplo
  posts: [
    {
      type: 'venta',
      title: 'Casa moderna en zona residencial exclusiva',
      description: 'Hermosa casa de dos plantas con acabados de lujo, ubicada en una zona residencial muy tranquila y segura. Cuenta con amplio jardín, cochera para dos autos y todas las comodidades modernas.',
      price: 2500000,
      location: 'norte',
      bedrooms: 4,
      bathrooms: 3,
      area: 250,
      images: [
        'https://images.unsplash.com/photo-1568605114967-8130f3a36994',
        'https://images.unsplash.com/photo-1570129477492-45c003edd2be'
      ]
    },
    {
      type: 'renta',
      title: 'Departamento céntrico con vista panorámica',
      description: 'Moderno departamento en el corazón de la ciudad con increíbles vistas panorámicas. Completamente amueblado y equipado, ideal para profesionales o parejas jóvenes.',
      price: 15000,
      location: 'centro',
      bedrooms: 2,
      bathrooms: 2,
      area: 85,
      images: [
        'https://images.unsplash.com/photo-1564013799919-ab600027ffc6',
        'https://images.unsplash.com/photo-1582268611958-ebfd161ef9cf'
      ]
    },
    {
      type: 'socio',
      title: 'Oportunidad de inversión en desarrollo inmobiliario',
      description: 'Buscamos socio inversionista para desarrollo de complejo residencial de lujo. Excelente ubicación y proyección de alta rentabilidad. Proyecto con permisos y estudios completos.',
      price: 5000000,
      location: 'oeste',
      area: 1000,
      images: [
        'https://images.unsplash.com/photo-1613490493576-7fde63acd811'
      ]
    },
    {
      type: 'intercambio',
      title: 'Casa en la playa por propiedad en la ciudad',
      description: 'Hermosa casa frente al mar, ideal para vacaciones o retiro. Busco intercambiar por propiedad en zona urbana de valor similar. La casa cuenta con acceso directo a playa privada.',
      price: 1800000,
      location: 'sur',
      bedrooms: 3,
      bathrooms: 2,
      area: 180,
      images: [
        'https://images.unsplash.com/photo-1568605114967-8130f3a36994'
      ]
    }
  ],

  // Comentarios de ejemplo
  comments: [
    {
      text: '¿Aceptan crédito hipotecario para esta propiedad?',
      authorIndex: 1
    },
    {
      text: 'Me interesa mucho, ¿podemos agendar una visita?',
      authorIndex: 2
    },
    {
      text: '¿El precio incluye los muebles?',
      authorIndex: 0
    },
    {
      text: 'Excelente ubicación, ¿tienen más propiedades similares?',
      authorIndex: 1
    },
    {
      text: '¿Cuánto es el pago de mantenimiento mensual?',
      authorIndex: 2
    }
  ],

  // Notificaciones de ejemplo
  notifications: [
    {
      title: 'Bienvenido a MulbinComponents',
      message: 'Gracias por unirte a nuestra plataforma inmobiliaria. Explora las mejores propiedades disponibles.',
      type: 'success',
      iconName: 'home'
    },
    {
      title: 'Nueva propiedad disponible',
      message: 'Se ha agregado una nueva propiedad que coincide con tus criterios de búsqueda.',
      type: 'info',
      iconName: 'notification'
    },
    {
      title: 'Comentario en tu publicación',
      message: 'Alguien ha comentado en tu publicación de casa en zona norte.',
      type: 'info',
      iconName: 'chatbubble'
    }
  ]
};

// Función para inicializar datos de desarrollo
const initializeDevData = async () => {
  console.log('🔄 Inicializando datos de desarrollo...');
  
  try {
    // Aquí se implementaría la lógica para insertar los datos
    // usando los métodos de Meteor o directamente las colecciones
    
    console.log('✅ Datos de desarrollo inicializados correctamente');
    return {
      success: true,
      message: 'Datos de desarrollo cargados',
      data: initDevData
    };
  } catch (error) {
    console.error('❌ Error al inicializar datos de desarrollo:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

// Exportar para uso en el servidor
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { initDevData, initializeDevData };
}
