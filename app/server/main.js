// Punto de entrada principal del servidor Meteor
import { Meteor } from "meteor/meteor";

// Importar configuración de startup
import '/imports/startup/server';

// Importar todas las APIs del servidor
import '/imports/api/collections';
import '/imports/api/methods';
import '/imports/api/publications';

// Script de pruebas removido temporalmente

if (Meteor.isServer) {
  Meteor.startup(() => {
    console.log("🚀 MulbinComponents Backend Server is running!");
    console.log(`📊 Environment: ${process.env.NODE_ENV || 'development'}`);
    console.log(`🔗 MongoDB URL: ${process.env.MONGO_URL ? 'Connected' : 'Not configured'}`);
  });
}



